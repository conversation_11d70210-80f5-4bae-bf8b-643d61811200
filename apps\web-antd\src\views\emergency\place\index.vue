<script lang="ts" setup>
import type { ShallowRef } from 'vue';
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { EmergencyPlace } from '#/api/core/emergency/place';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addEmergencyPlaceApi,
  deleteEmergencyPlaceApi,
  editEmergencyPlaceApi,
  getEmergencyPlaceDetailApi,
  getEmergencyPlaceListApi,
} from '#/api/core/emergency/place';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<EmergencyPlace>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<EmergencyPlace>,
  fromData: any,
) {
  return getEmergencyPlaceListApi(param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: EmergencyPlace) {
  return deleteEmergencyPlaceApi(row.facilityId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: EmergencyPlace) {
  gridFormRef.value?.gridApi.setLoading(true);
  getEmergencyPlaceDetailApi(row.facilityId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '周边场所基本信息',
        formData: {
          ...res,
          // 排水状况
          cmFacilityDrainageListSystemType: res.cmFacilityDrainage?.systemType,
          cmFacilityDrainageListDrainageCap:
            res.cmFacilityDrainage?.drainageCap,
          cmFacilityDrainageListCondition: res.cmFacilityDrainage?.condition,
          cmFacilityDrainageListDescription:
            res.cmFacilityDrainage?.description,

          // 消防设施状况
          cmFacilityFireSafetyListIsComplete:
            res.cmFacilityFireSafety?.isComplete,
          cmFacilityFireSafetyListDescription:
            res.cmFacilityFireSafety?.description,

          // 通风状况
          cmFacilityVentilationListSystemType:
            res.cmFacilityVentilation?.systemType,
          cmFacilityVentilationListDescription:
            res.cmFacilityVentilation?.description,

          // 应急物资状况
          cmFacilitySupplyStatusListSupplyLevel:
            res.cmFacilitySupplyStatus?.supplyLevel,
          cmFacilitySupplyStatusListSupplyTypes: res.cmFacilitySupplyStatus
            ?.supplyTypes
            ? JSON.parse(res.cmFacilitySupplyStatus?.supplyTypes)
            : [],
          cmFacilitySupplyStatusListDescription:
            res.cmFacilitySupplyStatus?.description,

          // 场所照片
          cmFacilityPhotoListPhotoPath: res.cmFacilityPhoto?.photoPath,
        },
        meta: { type: 'edit', raw: row },
      });
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: { formData: any; meta: any }): Promise<boolean> {
  // 排水状况
  const cmFacilityDrainage = {
    facilityId: param.meta.raw?.facilityId,
    systemType: param.formData.cmFacilityDrainageListSystemType,
    drainageCap: param.formData.cmFacilityDrainageListDrainageCap,
    condition: param.formData.cmFacilityDrainageListCondition,
    description: param.formData.cmFacilityDrainageListDescription,
  };
  param.formData.cmFacilityDrainage = cmFacilityDrainage;
  Reflect.deleteProperty(param.formData, 'cmFacilityDrainageListSystemType');
  Reflect.deleteProperty(param.formData, 'cmFacilityDrainageListDrainageCap');
  Reflect.deleteProperty(param.formData, 'cmFacilityDrainageListCondition');
  Reflect.deleteProperty(param.formData, 'cmFacilityDrainageListDescription');

  // 消防设施状况
  const cmFacilityFireSafety = {
    facilityId: param.meta.raw?.facilityId,
    isComplete: param.formData.cmFacilityFireSafetyListIsComplete,
    description: param.formData.cmFacilityFireSafetyListDescription,
  };
  param.formData.cmFacilityFireSafety = cmFacilityFireSafety;
  Reflect.deleteProperty(param.formData, 'cmFacilityFireSafetyListIsComplete');
  Reflect.deleteProperty(param.formData, 'cmFacilityFireSafetyListDescription');

  // 通风状况
  const cmFacilityVentilation = {
    facilityId: param.meta.raw?.facilityId,
    systemType: param.formData.cmFacilityVentilationListSystemType,
    description: param.formData.cmFacilityVentilationListDescription,
  };
  param.formData.cmFacilityVentilation = cmFacilityVentilation;
  Reflect.deleteProperty(param.formData, 'cmFacilityVentilationListSystemType');
  Reflect.deleteProperty(
    param.formData,
    'cmFacilityVentilationListDescription',
  );

  // 应急物资状况
  const cmFacilitySupplyStatus = {
    facilityId: param.meta.raw?.facilityId,
    supplyLevel: param.formData.cmFacilitySupplyStatusListSupplyLevel,
    supplyTypes: param.formData.cmFacilitySupplyStatusListSupplyTypes
      ? JSON.stringify(param.formData.cmFacilitySupplyStatusListSupplyTypes)
      : undefined,
    description: param.formData.cmFacilitySupplyStatusListDescription,
  };
  param.formData.cmFacilitySupplyStatus = cmFacilitySupplyStatus;
  Reflect.deleteProperty(
    param.formData,
    'cmFacilitySupplyStatusListSupplyLevel',
  );
  Reflect.deleteProperty(
    param.formData,
    'cmFacilitySupplyStatusListSupplyTypes',
  );
  Reflect.deleteProperty(
    param.formData,
    'cmFacilitySupplyStatusListDescription',
  );

  // 场所照片
  const cmFacilityPhoto = {
    facilityId: param.meta.raw?.facilityId,
    photoPath: param.formData.cmFacilityPhotoListPhotoPath,
  };
  param.formData.cmFacilityPhoto = cmFacilityPhoto;
  Reflect.deleteProperty(param.formData, 'cmFacilityPhotoListPhotoPath');

  if (param.meta.type === 'create')
    addEmergencyPlaceApi(param.formData)
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editEmergencyPlaceApi({
      ...param.meta.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="周边场所信息列表"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '周边场所基本信息',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
