/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-01
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\expert\data.ts
 */
import type { EnArea } from '#/api/core/energy-management/area-manage';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'areaName',
      label: '区域名称',
    },
    {
      fieldName: 'areaCode',
      label: '区域编号',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EnArea> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'areaCode',
      title: '区域编号',
    },
    {
      field: 'areaName',
      title: '区域名称',
    },
    {
      field: 'remark',
      title: '备注',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'areaCode',
      label: '区域编号',
      rules: 'required',
    },
    {
      fieldName: 'areaName',
      label: '区域名称',
      rules: 'required',
    },
    {
      fieldName: 'remark',
      label: '备注',
      component: 'Textarea',
    },
  ];
}
