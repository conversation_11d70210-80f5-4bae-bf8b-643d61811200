<script lang="ts" setup>
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { CmIncident } from '#/api/core/emergency/response';
import type { StatisticItem } from '#/components/statistic-card.vue';

import { h, onMounted, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import {
  EditOutlined,
  FieldTimeOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';
import { Button, Card, Col, Row, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  getEmergencyEventCountApi,
  getEmergencyEventListApi,
} from '#/api/core/emergency/response';
import StatisticCard from '#/components/statistic-card.vue';

import { SeverityMap, StatusMap } from './data';
import EventDetail from './event-detail.vue';

const eventDetailRef =
  useTemplateRef<ComponentExposed<typeof EventDetail>>('eventDetailRef');

onMounted(async () => {
  getEventList();
});

/** 新增事件 */
const addEvent = () => {
  eventDetailRef.value?.open();
};

/** 编辑指令 */
const editCommand = (record: CmIncident) => {
  eventDetailRef.value?.open(record);
};

/** 指令已更新 */
const handleEdit = async () => {
  getEventList();
};

const eventList = shallowRef<CmIncident[]>([]);
// 获取事件列表
function getEventList() {
  getEmergencyEventListApi(
    {
      currentPage: 1,
      pageSize: 999_999,
    },
    {},
  ).then((res) => {
    eventList.value = res.items;
  });
  getCountData();
}

// 统计
const total = ref<StatisticItem[]>([]);

function getCountData() {
  getEmergencyEventCountApi().then((res) => {
    for (const key of Object.keys(res.accidentTypeMap ?? {})) {
      total.value.push({
        color:
          (res.accidentTypeMap?.[key]?.changeFromYesterday ?? 0) >= 0
            ? '#dc2626'
            : '#16a34a',
        title: key,
        value: res.accidentTypeMap?.[key]?.count ?? 0,
        meta: res.accidentTypeMap?.[key],
        svgName: 'ph:warning-fill',
      });
    }
  });
}
</script>

<template>
  <Page auto-content-height>
    <div>
      <!-- 统计 -->
      <StatisticCard :total="total">
        <template #desc="{ item }">
          <p>
            <span>较昨日</span>
            <span
              :class="{
                'text-green-500': item.meta.changeFromYesterday < 0,
                'text-red-500': item.meta.changeFromYesterday >= 0,
              }"
            >
              {{ item.meta.changeFromYesterday >= 0 ? '+' : '-' }}
              {{ item.meta.changeFromYesterday ?? 0 }}
            </span>
          </p>
        </template>
      </StatisticCard>

      <Row :gutter="[24, 24]">
        <Col :span="24" :lg="8">
          <Card :bordered="false">
            <template #title>
              <div class="text-base">
                <span class="ml-2">应急事件列表</span>
              </div>
            </template>
            <div class="absolute right-6 top-3">
              <Button
                type="primary"
                shape="circle"
                :icon="h(PlusOutlined)"
                @click="addEvent"
              />
            </div>
            <div class="max-h-[60vh] overflow-y-auto">
              <div
                class="relative mt-3 p-2"
                v-for="item in eventList"
                :key="item.cmIncidentId"
                :style="{
                  borderLeft: '4px solid',
                  borderColor:
                    SeverityMap[item.severity ?? -1]?.color ?? 'gray',
                }"
              >
                <!-- 背景层（透明） -->
                <div
                  class="absolute inset-0 h-full w-full rounded-r-sm"
                  :style="{
                    backgroundColor: SeverityMap[item.severity ?? -1]?.color,
                  }"
                  style="opacity: 0.05"
                ></div>
                <!-- 状态定位在右上角 -->
                <div class="absolute right-3 top-3">
                  <Tag :color="StatusMap[item.status ?? -1]?.color ?? 'purple'">
                    {{
                      StatusMap[item.status ?? -1]?.label ?? item.status ?? ''
                    }}
                  </Tag>
                </div>
                <!--内容 -->
                <div class="font-bold">{{ item.cmIncidentType }}</div>
                <div>{{ item.description }}</div>
                <div class="flex justify-between">
                  <div class="mt-1">
                    <FieldTimeOutlined />
                    {{
                      item.occurTime
                        ? dayjs(item.occurTime).format('YYYY-MM-DD HH:mm:ss')
                        : ''
                    }}
                  </div>
                  <Button
                    type="link"
                    :icon="h(EditOutlined)"
                    @click="editCommand(item)"
                  />
                </div>
              </div>
            </div>
          </Card>
          <!-- <Card class="mt-6" title="事件进展时间线">
            <Timeline>
              <TimelineItem
                v-for="item in manageObj?.warningHistoryList"
                :key="item.recordId"
                :color="PriorityMap[item.level ?? '']?.color"
              >
                <div class="flex justify-between">
                  <div class="label text-base">{{ item.title }}</div>
                  <div>
                    {{
                      item.createTime
                        ? dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
                        : ''
                    }}
                  </div>
                </div>
                <div class="mt-3 text-sm">{{ item.overview }}</div>
              </TimelineItem>
            </Timeline>
          </Card> -->
        </Col>
        <Col :span="24" :lg="16">
          <Card title="三维地图监控">
            <div class="h-[200px]">地图内容</div>
          </Card>
          <Card title="三维地图监控" class="mt-6">
            <div class="h-[200px]">监控内容</div>
          </Card>
        </Col>
      </Row>
    </div>
    <EventDetail ref="eventDetailRef" @had-edit="handleEdit" />
  </Page>
</template>
<style lang="css" scoped>
.label {
  color: oklch(44.6% 0.043 257.281deg);
}
</style>
