<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CarbonEmissionStatistic } from '#/api/core/energy-management/carbon-emission-statistic';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addCarbonEmissionStatisticApi,
  deleteCarbonEmissionStatisticApi,
  editCarbonEmissionStatisticApi,
  getCarbonEmissionStatisticListApi,
} from '#/api/core/energy-management/carbon-emission-statistic';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import {
  getColumnsScheme,
  getFormOption,
  getFormOptionByEdit,
  getTopSearchScheme,
} from './data';
import Detail from './detail.vue';

// 获取路由的query参数
const route = useRoute();
const isShow = route.query.type === 'show';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');
const detailRef = ref<ComponentExposed<typeof Detail>>();

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<CarbonEmissionStatistic>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<CarbonEmissionStatistic>,
  fromData: any,
) {
  if (fromData.dateRange) {
    fromData.startTime = fromData.dateRange[0].startOf('day').toISOString();
    fromData.endTime = fromData.dateRange[1].endOf('day').toISOString();
    delete fromData.dateRange;
  }

  if (isShow) {
    fromData.status = 1;
  }

  return getCarbonEmissionStatisticListApi(param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: CarbonEmissionStatistic) {
  return deleteCarbonEmissionStatisticApi(row.id).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: CarbonEmissionStatistic) {
  formOption.value = getFormOptionByEdit();
  gridFormRef.value?.formModalRef?.open({
    title: '编辑碳排放计算单',
    formData: row,
    meta: { type: 'edit', raw: row },
  });
}

/** 审核 */
function auditPersonnel(row: CarbonEmissionStatistic) {
  row.status = 1;
  editCarbonEmissionStatisticApi(row).then(() => {
    message.success('审核成功');
  });
}

/** 查看 */
function viewPersonnel(row: CarbonEmissionStatistic) {
  detailRef.value?.open(row);
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: CarbonEmissionStatistic;
}): Promise<boolean> {
  param.formData.date = dayjs(param.formData.date).startOf('month');

  if (param.meta.type === 'create') {
    param.formData.status = 0;

    addCarbonEmissionStatisticApi({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  } else if (param.meta.type === 'edit')
    editCarbonEmissionStatisticApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="碳排放计算单列表"
        style="height: calc(100% - 160px)"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :top-search-init-value="{
          dateRange: [dayjs().startOf('year'), dayjs().endOf('year')],
        }"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            v-if="!isShow"
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                formOption = getFormOption();
                gridFormRef?.formModalRef?.open({
                  title: '新增碳排放计算单',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button
            v-if="row.status === 0"
            type="link"
            @click="editPersonnel(row)"
          >
            编辑
          </Button>
          <Button
            v-if="row.status === 1"
            type="link"
            @click="viewPersonnel(row)"
          >
            查看
          </Button>
          <Button
            v-if="row.status === 0"
            type="link"
            @click="auditPersonnel(row)"
          >
            审核
          </Button>
          <RemoveButton
            v-if="row.status === 0"
            @confirm="deletePersonnel(row)"
          />
        </template>
      </GridForm>
    </template>

    <Detail ref="detailRef" @close="gridFormRef?.gridApi.query()" />
  </Page>
</template>
