import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * CmTeamInfo对象
 *
 * CmTeamInfo
 */
export type CmTeamInfo = {
  /**
   * 联系电话号码
   */
  contactPhone?: string;
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 队伍详细描述
   */
  description?: string;
  /**
   * 电子邮箱地址
   */
  email?: string;
  /**
   * 装备信息
   */
  equipmentInfoList?: CmEquipmentInfo[];
  /**
   * 队伍成立日期
   */
  establishDate?: string;
  /**
   * 所属厂区代码
   */
  factoryArea?: string;
  /**
   * 专职人员数量
   */
  fullTime?: number;
  /**
   * 队伍负责人姓名
   */
  leader?: string;
  /**
   * 兼职人员数量
   */
  partTime?: number;
  /**
   * 其他备注信息
   */
  remarks?: string;
  /**
   * 专长领域
   */
  specialtyName?: string;
  /**
   * 队伍唯一标识符
   */
  teamId: string;
  /**
   * 队伍全称
   */
  teamName?: string;
  /**
   * 队伍当前状态（默认active）
   */
  teamStatus?: string;
  /**
   * 队伍类型代码
   */
  teamType?: string;
  /**
   * 技术人员数量
   */
  techStaff?: number;
  /**
   * 队伍总人数
   */
  totalMembers?: number;
  /**
   * 记录更新时间（自动更新）
   */
  updateTime?: string;
};

/**
 * CmEquipmentInfo对象
 *
 * CmEquipmentInfo
 */
export type CmEquipmentInfo = {
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 装备唯一标识符
   */
  equipmentId: string;
  /**
   * 装备名称
   */
  equipmentName?: string;
  /**
   * 装备类型
   */
  equipmentType?: string;
  /**
   * 生产厂家
   */
  manufacturer?: string;
  /**
   * 规格型号
   */
  model?: string;
  /**
   * 采购日期
   */
  purchaseDate?: string;
  /**
   * 装备数量（默认0）
   */
  quantity?: number;
  /**
   * 备注信息
   */
  remarks?: string;
  /**
   * 装备状态（默认normal）
   */
  status?: string;
  /**
   * 所属队伍ID
   */
  teamId: string;
  /**
   * 记录更新时间（自动更新）
   */
  updateTime?: string;
};

/**
 * 获取应急队伍列表
 */
export async function getEmergencyTeamListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: CmTeamInfo[];
    total: number;
  }>('/cm-team-info', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/**
 * 获取应急队伍详情
 */
export async function getEmergencyTeamDetailApi(teamId: string) {
  return requestClient.get<CmTeamInfo>(`/cm-team-info/detail/${teamId}`);
}

/**
 * 新增应急队伍
 */
export async function addEmergencyTeamApi(data: CmTeamInfo) {
  return requestClient.post<CmTeamInfo>('/cm-team-info', data);
}

/**
 * 修改应急队伍
 */
export async function editEmergencyTeamApi(data: CmTeamInfo) {
  return requestClient.put<CmTeamInfo>('/cm-team-info', data);
}

/**
 * 删除应急队伍
 */
export async function deleteEmergencyTeamApi(teamId: string) {
  return requestClient.delete<CmTeamInfo>(`/cm-team-info/${teamId}`);
}
