import { requestClient } from '#/api/request';

/**
 * 时段类型
 * EnPeriodType
 */
export type EnPeriodType = {
  description: string;
  periodColor: string;
  periodName: string;
  periodTypeId: number;
  sortOrder: number;
};

/**
 * @returns 时段类型列表
 */
export async function getPeriodTypeListApi(params: any) {
  return requestClient.get<EnPeriodType[]>(
    '/energyManagementBaseData/periodType/list',
    {
      params,
    },
  );
}

/**
 * 新增时段类型
 */
export async function addPeriodTypeApi(data: EnPeriodType) {
  return requestClient.post<EnPeriodType>(
    '/energyManagementBaseData/periodType',
    data,
  );
}

/**
 * 修改时段类型
 */
export async function editPeriodTypeApi(data: EnPeriodType) {
  return requestClient.put<EnPeriodType>(
    '/energyManagementBaseData/periodType',
    data,
  );
}

/**
 * 删除时段类型
 */
export async function deletePeriodTypeApi(periodTypeId: number) {
  return requestClient.delete<string>(
    `/energyManagementBaseData/periodType/${periodTypeId}`,
  );
}
