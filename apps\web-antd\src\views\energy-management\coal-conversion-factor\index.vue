<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CoalConversionFactorDTO } from '#/api/core/energy-management/coal-conversion-factor';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, onUnmounted, ref, shallowRef, useTemplateRef } from 'vue';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';
import * as echarts from 'echarts';

import {
  addCoalConversionFactorApi,
  deleteCoalConversionFactorApi,
  editCoalConversionFactorApi,
  getCoalConversionFactorListApi,
} from '#/api/core/energy-management/coal-conversion-factor';
import { getEnergyMediumTypeListApi } from '#/api/core/energy-management/energy-medium-type';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const columnsScheme: ShallowRef<CustomColumnSchema<CoalConversionFactorDTO>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(
  getFormOption([]),
);

// 获取能源介质数据
getEnergyMediumTypeListApi({ status: 1 }).then((mediumTypes) => {
  formOption.value = getFormOption(mediumTypes);
});

/**
 * @description: 获取表格数据
 */
async function query(
  _param: VxeGridPropTypes.ProxyAjaxQueryParams<CoalConversionFactorDTO>,
  _fromData: any,
) {
  const res = await getCoalConversionFactorListApi();
  initChart(res);
  return {
    items: res,
    total: res.length,
  };
}

const chart = ref<echarts.ECharts | null>(null);
function initChart(data: CoalConversionFactorDTO[]) {
  chart.value = echarts.init(
    document.querySelector('#coal-conversion-factor-chart') as HTMLElement,
  );

  // 柱状图
  chart.value.setOption({
    title: {
      text: '标煤折算系数对比',
    },
    // legend: {
    //   data: data.map((item) => item.mediumName),
    // },
    tooltip: {},
    xAxis: {
      type: 'category',
      data: data.map((item) => item.mediumName),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        type: 'bar',
        barWidth: '100',
        data: data.map((item) => item.conversionFactor),
      },
    ],
  });

  onUnmounted(() => {
    chart.value?.dispose();
    chart.value = null;
  });
}

/**
 * @description: 删除
 */
function deleteCoalConversionFactor(row: CoalConversionFactorDTO) {
  return deleteCoalConversionFactorApi(row.factorId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  return param.meta.type === 'create'
    ? addCoalConversionFactorApi(param.formData)
        .then(() => {
          message.success('新增成功');
          gridFormRef.value?.gridApi.reload();
          return true;
        })
        .catch(() => false)
    : editCoalConversionFactorApi({
        ...param.raw,
        ...param.formData,
      })
        .then(() => {
          message.success('更新成功');
          gridFormRef.value?.gridApi.query();
          return true;
        })
        .catch(() => false);
}
</script>

<template>
  <div class="h-full p-4">
    <div class="flex h-full flex-col bg-white">
      <GridForm
        ref="gridFormRef"
        class="flex-1"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :disable-page="true"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '标煤折算系数信息',
                meta: { type: 'create' },
              })
            "
          />
        </template>
        <template #action="{ row }">
          <Button
            type="link"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '标煤折算系数信息',
                formData: row,
                meta: { type: 'edit' },
              })
            "
          >
            编辑
          </Button>
          <RemoveButton @confirm="deleteCoalConversionFactor(row)" />
        </template>
      </GridForm>
      <div id="coal-conversion-factor-chart" class="w-full flex-1 p-4"></div>
    </div>
  </div>
</template>
