<script lang="ts" setup>
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { QRcodeType } from './core.vue';

import { useTemplateRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import QRcodeCore from './core.vue';

const title = defineModel<string>('title', { default: '二维码' });
const qrContent = defineModel<QRcodeType>('qrContent', {
  default: { text: '' },
});

const QRcodeRef =
  useTemplateRef<ComponentExposed<typeof QRcodeCore>>('QRcodeRef');

const [Modal, modalApi] = useVbenModal({
  showConfirmButton: true,
  confirmText: '下载二维码',
  onConfirm() {
    QRcodeRef.value?.downloadQr();
  },
});

function open(param?: { qrContent?: QRcodeType; title?: string }) {
  if (param?.qrContent) qrContent.value = param?.qrContent;
  if (param?.title) title.value = param?.title;
  modalApi.open();
}

defineExpose({
  open,
});
</script>
<template>
  <div>
    <Modal class="w-[300px]" :title>
      <div v-if="qrContent" class="flex items-center justify-center">
        <QRcodeCore :qr-content ref="QRcodeRef" :download-name="title" />
      </div>
    </Modal>
  </div>
</template>
