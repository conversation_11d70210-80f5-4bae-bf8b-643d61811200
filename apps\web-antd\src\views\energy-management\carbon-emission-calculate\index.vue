<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { EnCarbonEmissionTemplate } from './data';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { EnDevice } from '#/api/core/energy-management/device-manage';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addCarbonEmissionTemplateApi,
  deleteCarbonEmissionTemplateApi,
  editCarbonEmissionTemplateApi,
  getCarbonEmissionTemplatePageApi,
  getDeviceListApi,
} from '#/api/core/energy-management/carbon-emission-template';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const columnsScheme: ShallowRef<CustomColumnSchema<EnCarbonEmissionTemplate>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef([]);

const deviceList = ref<EnDevice[]>([]);
getDeviceListApi().then((res) => {
  deviceList.value = res;
  formOption.value = getFormOption(deviceList.value);
});

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<EnCarbonEmissionTemplate>,
  formData: any,
) {
  const queryParams = {
    pageNumber: param.page?.currentPage,
    pageSize: param.page?.pageSize,
    ...formData,
  };

  const res = await getCarbonEmissionTemplatePageApi(queryParams);
  return {
    items: res.data,
    total: res.total,
  };
}

/**
 * @description: 删除模板
 */
function deleteTemplate(row: EnCarbonEmissionTemplate) {
  return deleteCarbonEmissionTemplateApi(row.templateId!).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑或新建模板
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  const templateData = param.formData.templateData.map((item: any) => ({
    deviceId: item.deviceId,
    deviceName: deviceList.value.find(
      (device) => device.deviceId === item.deviceId,
    )?.deviceName,
    nodeData: item.nodeData,
    metrics: item.metrics,
  }));

  const reqData = {
    templateName: param.formData.templateName,
    templateData: JSON.stringify(templateData),
    status: param.meta.type === 'create' ? 0 : param.raw.status,
  };

  return param.meta.type === 'create'
    ? addCarbonEmissionTemplateApi(reqData)
        .then(() => {
          message.success('新增成功');
          gridFormRef.value?.gridApi.reload();
          return true;
        })
        .catch(() => {
          message.error('新增失败');
          return false;
        })
    : editCarbonEmissionTemplateApi({
        ...param.raw,
        ...reqData,
      })
        .then(() => {
          message.success('更新成功');
          gridFormRef.value?.gridApi.query();
          return true;
        })
        .catch(() => {
          message.error('更新失败');
          return false;
        });
}

function enableTemplate(row: EnCarbonEmissionTemplate) {
  return editCarbonEmissionTemplateApi({
    ...row,
    status: 1,
  })
    .then(() => {
      message.success('启用成功');
      gridFormRef.value?.gridApi.query();
    })
    .catch(() => {
      message.error('启用失败');
    });
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="碳排放模板"
        modal-class="w-[800px]"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :disable-page="false"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '新增碳排放模板',
                meta: { type: 'create' },
              })
            "
          />
        </template>
        <template #action="{ row }">
          <Button
            v-if="row.status === 0"
            type="link"
            @click="enableTemplate(row)"
          >
            启用
          </Button>
          <Button
            type="link"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '编辑碳排放模板',
                formData: {
                  ...row,
                  templateData: JSON.parse(row.templateData),
                },
                meta: { type: 'edit' },
              })
            "
          >
            编辑
          </Button>
          <RemoveButton @confirm="deleteTemplate(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
