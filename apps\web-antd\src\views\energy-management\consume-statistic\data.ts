import type { PriceCountDTO } from '#/api/core/energy-management/consume-statistic';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-01
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\expert\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { getAreaListApi } from '#/api/core/energy-management/area-manage';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    // 时间范围
    {
      fieldName: 'timeRange',
      label: '时间范围',
      component: 'RangePicker',
      componentProps: {
        showTime: true,
        placeholder: ['默认今日开始时间', '默认今日结束时间'],
      },
    },
    // 区域
    {
      fieldName: 'areaId',
      label: '区域',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getAreaListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              areaName: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.areaName ?? '',
            value: item.areaId,
          }));
        },
      },
    },
    // 类型
    {
      fieldName: 'water',
      label: '类型',
      component: 'Select',
      componentProps: {
        options: [
          { label: '水', value: 1 },
          { label: '电', value: 2 },
        ],
        placeholder: '默认水',
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<PriceCountDTO> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    // 开始时间
    {
      field: 'startTime',
      title: '开始时间',
      customType: 'date',
    },
    // 结束时间
    {
      field: 'endTime',
      title: '结束时间',
      customType: 'date',
    },
    // 区域
    {
      field: 'areaName',
      title: '区域',
    },
    // 设备
    {
      field: 'deviceName',
      title: '设备',
    },
    // 用量
    {
      field: 'usage',
      title: '用量',
    },
    // 费用
    {
      field: 'price',
      title: '费用(元)',
    },
  ];
}
