/*
 * @Author: Strayer
 * @Date: 2025-08-15
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-15
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\api\core\energy-management\alarm-config.ts
 */

import { requestClient } from '#/api/request';

/**
 * 告警配置
 */
export type WarningConfigDTO = {
  /**
   * 区域ID
   */
  areaId?: string;
  /**
   * 区域名称
   */
  areaName?: string;
  /**
   * 告警配置唯一标识符
   */
  configId: string;
  /**
   * 设备ID
   */
  deviceId?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 干系人
   */
  name?: string;
  /**
   * 周期（1:日，2:月，3:年）
   */
  period?: number;
  /**
   * 范围
   */
  range?: number;
  /**
   * 类型（1:水，2:电，3:标煤，4:碳排放）
   */
  type?: number;
  /**
   * 标准值
   */
  value?: number;
};

/**
 * @description 获取告警配置列表
 * @param type 4:碳排放，NULL：其他3种
 */
export async function getAlarmConfigListApi(type?: number) {
  return requestClient.get<WarningConfigDTO[]>('/carbon/warningConfig/list', {
    params: { type },
  });
}

/** 新增告警配置 */
export async function addAlarmConfigApi(data: WarningConfigDTO) {
  return requestClient.post<WarningConfigDTO>('/carbon/warningConfig', data);
}

/** 修改告警配置 */
export async function editAlarmConfigApi(data: WarningConfigDTO) {
  return requestClient.put<WarningConfigDTO>('/carbon/warningConfig', data);
}

/** 删除告警配置 */
export async function deleteAlarmConfigApi(configId: string) {
  return requestClient.delete<WarningConfigDTO>(
    `/carbon/warningConfig/${configId}`,
  );
}
