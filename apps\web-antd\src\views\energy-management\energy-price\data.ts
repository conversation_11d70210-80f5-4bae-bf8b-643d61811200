import type { EnMediumType } from '#/api/core/energy-management/energy-medium-type';
import type { PriceDTO } from '#/api/core/energy-management/energy-price';
import type { EnPeriodType } from '#/api/core/energy-management/period-type';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'mediumId',
      label: '能源介质',
      component: 'Select',
      componentProps: {
        placeholder: '请选择能源介质',
        options: [],
      },
    },
    {
      fieldName: 'periodTypeId',
      label: '时段类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择时段类型',
        options: [],
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '有效', value: 1 },
          { label: '无效', value: 0 },
        ],
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(
  periodTypes: EnPeriodType[],
): CustomColumnSchema<PriceDTO> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'priceId', title: 'ID', visible: false },
    // 能源介质名称
    { field: 'mediumName', title: '能源介质' },
    // 时段类型名称
    {
      field: 'periodName',
      title: '时段类型',
      customType: 'tag',
      formatter: ((params: any) => {
        const periodType = periodTypes.find(
          (item) => item.periodTypeId === params.row.periodTypeId,
        );
        return [
          {
            label: params.row.periodName,
            color: periodType?.periodColor,
          },
        ];
      }) as any,
    },
    // 单价
    {
      field: 'price',
      title: '单价(元)',
      formatter: ({ cellValue }: { cellValue: number }) => {
        return cellValue ? cellValue.toFixed(2) : '';
      },
    },
    // 生效日期
    {
      field: 'effectiveDate',
      title: '生效日期',
      customType: 'dateDay',
    },
    // 失效日期
    {
      field: 'expiryDate',
      title: '失效日期',
      customType: 'dateDay',
    },
    // 状态
    {
      field: 'status',
      title: '状态',
      formatter: ({ cellValue }: { cellValue: number }) => {
        return cellValue === 1 ? '有效' : '无效';
      },
    },
    // 创建时间 默认隐藏 时间类型
    {
      field: 'createTime',
      title: '创建时间',
      customType: 'date',
      visible: false,
    },
    // 更新时间 默认隐藏
    {
      field: 'updateTime',
      title: '更新时间',
      customType: 'date',
      visible: false,
    },
    {
      field: 'action',
      title: '操作',
      width: 180,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(
  mediumTypeList: EnMediumType[] = [],
  periodTypeList: EnPeriodType[] = [],
): CustomFormSchema[] {
  return [
    {
      fieldName: 'mediumId',
      label: '能源介质',
      rules: 'required',
      component: 'Select',
      componentProps: {
        placeholder: '请选择能源介质',
        options: mediumTypeList.map((item) => ({
          label: item.mediumName,
          value: item.mediumId,
        })),
      },
    },
    {
      fieldName: 'periodTypeId',
      label: '时段类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择时段类型（可选）',
        options: periodTypeList.map((item) => ({
          label: item.periodName,
          value: item.periodTypeId,
        })),
        allowClear: true,
      },
    },
    {
      fieldName: 'price',
      label: '单价',
      rules: 'required',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入单价',
        min: 0,
        precision: 2,
        addonAfter: '元',
      },
    },
    {
      fieldName: 'effectiveDate',
      label: '生效日期',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择生效日期',
        format: 'YYYY-MM-DD',
      },
    },
    {
      fieldName: 'expiryDate',
      label: '失效日期',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择失效日期（可选）',
        format: 'YYYY-MM-DD',
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      rules: 'required',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '有效', value: 1 },
          { label: '无效', value: 0 },
        ],
      },
    },
  ];
}
