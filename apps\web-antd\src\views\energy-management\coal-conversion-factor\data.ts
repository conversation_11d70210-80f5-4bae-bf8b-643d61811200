import type { CoalConversionFactorDTO } from '#/api/core/energy-management/coal-conversion-factor';
import type { EnMediumType } from '#/api/core/energy-management/energy-medium-type';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'mediumId',
      label: '能源介质',
      component: 'Select',
      componentProps: {
        placeholder: '请选择能源介质',
        options: [],
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '有效', value: 1 },
          { label: '无效', value: 0 },
        ],
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CoalConversionFactorDTO> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'factorId', title: 'ID', visible: false },
    // 能源介质名称
    { field: 'mediumName', title: '能源介质' },
    // 折算系数
    {
      field: 'conversionFactor',
      title: '折算系数',
      formatter: ({ cellValue }: { cellValue: number }) => {
        return cellValue ? cellValue.toFixed(4) : '';
      },
    },
    // 系数来源
    { field: 'source', title: '系数来源' },
    // 生效日期
    {
      field: 'effectiveDate',
      title: '生效日期',
      customType: 'dateDay',
    },
    // 状态
    {
      field: 'status',
      title: '状态',
      formatter: ({ cellValue }: { cellValue: number }) => {
        return cellValue === 1 ? '有效' : '无效';
      },
    },
    // 创建时间 默认隐藏 时间类型
    {
      field: 'createTime',
      title: '创建时间',
      customType: 'date',
      visible: false,
    },
    // 更新时间 默认隐藏
    {
      field: 'updateTime',
      title: '更新时间',
      customType: 'date',
      visible: false,
    },
    {
      field: 'action',
      title: '操作',
      width: 180,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(
  mediumTypeList: EnMediumType[] = [],
): CustomFormSchema[] {
  return [
    {
      fieldName: 'mediumId',
      label: '能源介质',
      rules: 'required',
      component: 'Select',
      componentProps: {
        placeholder: '请选择能源介质',
        options: mediumTypeList.map((item) => ({
          label: item.mediumName,
          value: item.mediumId,
        })),
      },
    },
    {
      fieldName: 'conversionFactor',
      label: '折算系数',
      rules: 'required',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入折算系数',
        min: 0,
        precision: 4,
      },
    },
    {
      fieldName: 'source',
      label: '系数来源',
      rules: 'required',
      component: 'Input',
      componentProps: {
        placeholder: '请输入系数来源',
      },
    },
    {
      fieldName: 'effectiveDate',
      label: '生效日期',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择生效日期',
        format: 'YYYY-MM-DD',
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      rules: 'required',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '有效', value: 1 },
          { label: '无效', value: 0 },
        ],
      },
    },
  ];
}
