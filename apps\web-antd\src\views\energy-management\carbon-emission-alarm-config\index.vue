<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { WarningConfigDTO } from '#/api/core/energy-management/alarm-config';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addAlarmConfigApi,
  deleteAlarmConfigApi,
  editAlarmConfigApi,
  getAlarmConfigListApi,
} from '#/api/core/energy-management/alarm-config';
import { getAreaListApi } from '#/api/core/energy-management/area-manage';
import { getDeviceListApi } from '#/api/core/energy-management/device-manage';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const columnsScheme: ShallowRef<CustomColumnSchema<WarningConfigDTO>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query() {
  return getAlarmConfigListApi(4).then((res) => {
    return {
      items: res,
      total: res.length,
    };
  });
}

/**
 * @description: 删除
 */
function deletePersonnel(row: WarningConfigDTO) {
  return deleteAlarmConfigApi(row.configId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: WarningConfigDTO) {
  gridFormRef.value?.formModalRef?.open({
    title: '编辑告警配置',
    formData: row,
    meta: { type: 'edit', raw: row },
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
async function onSubmit(param: {
  formData: any;
  meta: any;
  raw: WarningConfigDTO;
}): Promise<boolean> {
  // 区域名称
  const areaList = await getAreaListApi(
    {
      currentPage: 1,
      pageSize: 999_999,
    },
    {},
  );
  param.formData.areaName = areaList.items.find(
    (item) => item.areaId === param.formData.areaId,
  )?.areaName;

  // 用能设备名称
  const deviceList = await getDeviceListApi(
    {
      currentPage: 1,
      pageSize: 999_999,
    },
    {},
  );
  param.formData.deviceName = deviceList.items.find(
    (item) => item.deviceId === param.formData.deviceId,
  )?.deviceName;

  // 用能类型
  param.formData.type = 4;

  if (param.meta.type === 'create')
    addAlarmConfigApi({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editAlarmConfigApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return true;
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="现有告警配置"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :disable-page="true"
        :query="query"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增告警配置',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
