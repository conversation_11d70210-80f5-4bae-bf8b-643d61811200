<script lang="ts" setup>
import type { ShallowRef } from 'vue';
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { EventNotification } from '#/api/core/accident-alarm/notice';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addEventNoticeApi,
  deleteEventNoticeApi,
  editEventNoticeApi,
  getEventNoticeDetailApi,
  getEventNoticeListApi,
} from '#/api/core/accident-alarm/notice';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<EventNotification>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<EventNotification>,
  fromData: any,
) {
  return getEventNoticeListApi(param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: EventNotification) {
  return deleteEventNoticeApi(row.notificationId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: EventNotification) {
  gridFormRef.value?.gridApi.setLoading(true);
  getEventNoticeDetailApi(row.notificationId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '编辑事件通知',
        formData: {
          ...res,
          notificationRecipients: res.notificationRecipients?.map(
            (item) => item.receiverName,
          ),
        },
        meta: { type: 'edit', raw: row },
      });
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: EventNotification;
}): Promise<boolean> {
  if (param.formData.notificationRecipients) {
    param.formData.notificationRecipients =
      param.formData.notificationRecipients.map((item: any) => ({
        receiverName: item,
        notificationId: param.meta.raw?.notificationId,
      }));
  }

  if (param.meta.type === 'create')
    addEventNoticeApi({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editEventNoticeApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="事件通知列表"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增事件通知',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
