<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { PriceDTO } from '#/api/core/energy-management/energy-price';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import { getEnergyMediumTypeListApi } from '#/api/core/energy-management/energy-medium-type';
import {
  addEnergyPriceApi,
  deleteEnergyPriceApi,
  editEnergyPriceApi,
  getEnergyPriceListApi,
} from '#/api/core/energy-management/energy-price';
import { getPeriodTypeListApi } from '#/api/core/energy-management/period-type';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const columnsScheme: ShallowRef<CustomColumnSchema<PriceDTO>> = shallowRef(
  getColumnsScheme([]),
);
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(
  getFormOption([], []),
);

// 获取能源介质和时段类型数据
Promise.all([
  getEnergyMediumTypeListApi({ status: 1 }),
  getPeriodTypeListApi({}),
]).then(([mediumTypes, periodTypes]) => {
  // 根据sortOrder排序时段类型
  periodTypes.sort((a, b) => a.sortOrder - b.sortOrder);
  formOption.value = getFormOption(mediumTypes, periodTypes);
  columnsScheme.value = getColumnsScheme(periodTypes);
});

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<PriceDTO>,
  fromData: any,
) {
  const res = await getEnergyPriceListApi(fromData);
  return {
    items: res,
    total: res.length,
  };
}

/**
 * @description: 删除
 */
function deleteEnergyPrice(row: PriceDTO) {
  return deleteEnergyPriceApi(row.priceId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  return param.meta.type === 'create'
    ? addEnergyPriceApi(param.formData)
        .then(() => {
          message.success('新增成功');
          gridFormRef.value?.gridApi.reload();
          return true;
        })
        .catch(() => false)
    : editEnergyPriceApi({
        ...param.raw,
        ...param.formData,
      })
        .then(() => {
          message.success('更新成功');
          gridFormRef.value?.gridApi.query();
          return true;
        })
        .catch(() => false);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :disable-page="true"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '能源单价信息',
                meta: { type: 'create' },
              })
            "
          />
        </template>
        <template #action="{ row }">
          <Button
            type="link"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '能源单价信息',
                formData: row,
                meta: { type: 'edit' },
              })
            "
          >
            编辑
          </Button>
          <RemoveButton @confirm="deleteEnergyPrice(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
