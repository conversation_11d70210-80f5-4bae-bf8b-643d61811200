<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type {
  ElectricityDTO,
  ElectricityUsageRecordDTO,
} from '#/api/core/energy-management/electric-statistic';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';
import type { StatisticItem } from '#/components/statistic-card.vue';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import {
  FallOutlined,
  PlusOutlined,
  RiseOutlined,
} from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addElectricStatisticApi,
  deleteElectricStatisticApi,
  editElectricStatisticApi,
  getElectricStatisticListApi,
} from '#/api/core/energy-management/electric-statistic';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';
import StatisticCard from '#/components/statistic-card.vue';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');
const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<ElectricityUsageRecordDTO>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<ElectricityUsageRecordDTO>,
  fromData: any,
) {
  fromData.recordDate = (fromData.recordDate ?? dayjs())
    .startOf('day')
    .toISOString();
  const res = await getElectricStatisticListApi(param.page, fromData);

  dealCountData(res.electricityList ?? []);

  return {
    total: res.electricityUsageRecordList?.total ?? 0,
    items: res.electricityUsageRecordList?.records ?? [],
  };
}

/**
 * @description: 删除
 */
function deletePersonnel(row: ElectricityUsageRecordDTO) {
  return deleteElectricStatisticApi(row.recordId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: ElectricityUsageRecordDTO) {
  gridFormRef.value?.formModalRef?.open({
    title: '编辑用电计量',
    formData: row,
    meta: { type: 'edit', raw: row },
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: ElectricityUsageRecordDTO;
}): Promise<boolean> {
  param.formData.startTime = dayjs(param.formData.startTime).startOf('h');
  param.formData.endTime = dayjs(param.formData.endTime).startOf('h');

  if (param.meta.type === 'create')
    addElectricStatisticApi({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editElectricStatisticApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}

const total = ref<StatisticItem[]>([]);

function dealCountData(data: ElectricityDTO[]) {
  const iconMap: Record<number, string> = {
    0: 'pepicons-pop:electricity-circle-filled',
    1: 'icon-park-outline:electric-wave',
    2: 'emojione-monotone:electric-plug',
    3: 'material-symbols:electric-meter',
    4: 'streamline-plump:electric-charging-station-remix',
  };

  total.value = data.map((item, index) => ({
    title: item.areaName ?? '',
    value: `${item.electricityUsage ?? 0} KWH`,
    svgName: iconMap[index] ?? 'material-symbols:water-drop',
    meta: {
      changePercentage: item.electricityUsageRatio,
    },
    color: 'blue',
  }));
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <StatisticCard :total="total">
        <template #desc="{ item }">
          <p>
            <RiseOutlined
              v-if="item.meta.changePercentage >= 0"
              class="text-red-500"
            />
            <FallOutlined
              v-if="item.meta.changePercentage < 0"
              class="text-green-500"
            />
            <span
              :class="{
                'text-green-500': item.meta.changePercentage < 0,
                'text-red-500': item.meta.changePercentage >= 0,
              }"
            >
              {{ item.meta.changePercentage ?? 0 }}%
            </span>
            <span>较昨日</span>
          </p>
        </template>
      </StatisticCard>
      <GridForm
        ref="gridFormRef"
        style="height: calc(100vh - 250px); min-height: 500px"
        title="详细用电记录"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
        :form-config="{
          wrapperClass: 'grid-cols-2',
        }"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增用电计量',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
