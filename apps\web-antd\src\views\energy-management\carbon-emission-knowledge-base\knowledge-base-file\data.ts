/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-20
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\energy-management\carbon-emission-knowledge-base\knowledge-base-file\data.ts
 */
import type { EnCarbonEmissionKnowledgeFile } from '#/api/core/energy-management/carbon-emission-knowledge-base';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EnCarbonEmissionKnowledgeFile> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'filePath',
      title: '文件',
      formatter: ({ row }: { row: EnCarbonEmissionKnowledgeFile }) => {
        return (
          (row.filePath ? row.filePath.split('/').pop() : undefined) ?? row.id
        );
      },
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'filePath',
      label: '文件',
      rules: 'required',
      component: 'Upload',
    },
  ];
}
