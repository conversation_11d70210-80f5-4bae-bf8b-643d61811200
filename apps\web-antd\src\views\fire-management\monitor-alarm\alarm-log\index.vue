<script lang="ts" setup>
import type { ShallowRef } from 'vue';
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { FfEquipmentAlarmRecord } from '#/api/core/fire-management/monitor-alarm';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addAlarmLogApi,
  deleteAlarmLogApi,
  editAlarmLogApi,
  getAlarmLogListApi,
} from '#/api/core/fire-management/monitor-alarm';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const props = defineProps<{
  deviceId: string;
}>();

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<FfEquipmentAlarmRecord>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<FfEquipmentAlarmRecord>,
  fromData: any,
) {
  if (!props.deviceId)
    return {
      total: 0,
      items: [],
    };
  return getAlarmLogListApi(props.deviceId, param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: FfEquipmentAlarmRecord) {
  return deleteAlarmLogApi(row.alarmId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: FfEquipmentAlarmRecord) {
  gridFormRef.value?.formModalRef?.open({
    title: '编辑告警记录',
    formData: row,
    meta: { type: 'edit' },
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  param.formData.deviceId = props.deviceId;

  switch (param.meta.type) {
    case 'create': {
      return addAlarmLogApi(param.formData)
        .then(() => {
          message.success('新增成功');
          gridFormRef.value?.gridApi.reload();
          return true;
        })
        .catch(() => false);
    }
    case 'edit': {
      return editAlarmLogApi({
        ...param.raw,
        ...param.formData,
      })
        .then(() => {
          message.success('更新成功');
          gridFormRef.value?.gridApi.query();
          return true;
        })
        .catch(() => false);
    }
    default: {
      return Promise.resolve(false);
    }
  }
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="告警记录列表"
        back-route="/fire-management/monitor-alarm"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '新增告警记录',
                meta: { type: 'create' },
              })
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
