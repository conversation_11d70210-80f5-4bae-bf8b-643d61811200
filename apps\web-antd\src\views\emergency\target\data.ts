/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-24
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\target\data.ts
 */
import type { EmergencyTarget } from '#/api/core/emergency/target';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '目标名称',
    },
    {
      fieldName: 'type',
      label: '目标类别',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EmergencyTarget> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    // 目标编码
    {
      field: 'targetId',
      title: '目标编码',
    },
    // 目标名称
    {
      field: 'name',
      title: '目标名称',
    },
    // 目标类别
    {
      field: 'category',
      title: '目标类别',
    },
    // 距离
    {
      field: 'distance',
      title: '距离(km)',
    },
    // 人员数量
    {
      field: 'population',
      title: '人员数量',
    },
    // 风险等级
    {
      field: 'riskLevel',
      title: '风险等级',
    },
    // 责任人
    {
      field: 'responsible',
      title: '责任人',
    },
    // 联系电话
    {
      field: 'contact',
      title: '联系电话',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '防护单位名称',
      rules: 'required',
    },
    {
      fieldName: 'category',
      label: '目标类别',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: [
          { label: '工业设施', value: '工业设施' },
          { label: '居民区', value: '居民区' },
          { label: '公共设施', value: '公共设施' },
          { label: '环境敏感区', value: '环境敏感区' },
          { label: '其他', value: '其他' },
        ],
      },
    },
    // 地址
    {
      fieldName: 'address',
      label: '地址',
      rules: 'required',
    },
    // 责任人
    {
      fieldName: 'responsible',
      label: '责任人',
      rules: 'required',
    },
    // 联系电话
    {
      fieldName: 'contact',
      label: '联系电话',
      rules: 'required',
    },
    // 距离
    {
      fieldName: 'distance',
      label: '与危险源的距离(km)',
      rules: 'required',
      component: 'InputNumber',
    },
    // 人员数量
    {
      fieldName: 'population',
      label: '人员数量',
      rules: 'required',
      component: 'InputNumber',
    },
    // 风险等级
    {
      fieldName: 'riskLevel',
      label: '风险等级',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: [
          { label: '高风险', value: '高风险' },
          { label: '中风险', value: '中风险' },
          { label: '低风险', value: '低风险' },
        ],
      },
    },
    // 防护级别
    {
      fieldName: 'protectionLevel',
      label: '防护级别',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: [
          { label: '一级防护', value: '一级防护' },
          { label: '二级防护', value: '二级防护' },
          { label: '三级防护', value: '三级防护' },
          { label: '四级防护', value: '四级防护' },
        ],
      },
    },
    // 经度
    {
      fieldName: 'longitude',
      label: '经度',
      rules: 'required',
    },
    // 纬度
    {
      fieldName: 'latitude',
      label: '纬度',
      rules: 'required',
    },
    // 描述
    {
      fieldName: 'description',
      label: '防护目标描述',
      component: 'Textarea',
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '应急资源需求',
    },
    // 应急资源
    {
      fieldName: 'cmProtectionTargetResourcesFireResource',
      label: '消防资源',
      component: 'Select',
      componentProps: {
        options: [
          { label: '不需要', value: '不需要' },
          { label: '少量', value: '少量' },
          { label: '中等', value: '中等' },
          { label: '大量', value: '大量' },
        ],
      },
    },
    {
      fieldName: 'cmProtectionTargetResourcesMedicalResource',
      label: '医疗资源',
      component: 'Select',
      componentProps: {
        options: [
          { label: '不需要', value: '不需要' },
          { label: '少量', value: '少量' },
          { label: '中等', value: '中等' },
          { label: '大量', value: '大量' },
        ],
      },
    },
    {
      fieldName: 'cmProtectionTargetResourcesEvacuationResource',
      label: '疏散资源',
      component: 'Select',
      componentProps: {
        options: [
          { label: '不需要', value: '不需要' },
          { label: '少量', value: '少量' },
          { label: '中等', value: '中等' },
          { label: '大量', value: '大量' },
        ],
      },
    },
    {
      fieldName: 'cmProtectionTargetResourcesOtherResources',
      label: '其他资源',
      component: 'Textarea',
    },
    // 照片
    {
      component: 'Divider',
      fieldName: '',
      label: '照片',
    },
    {
      fieldName: 'cmProtectionTargetPhoto',
      label: '照片',
      component: 'UploadImg',
    },
  ];
}
