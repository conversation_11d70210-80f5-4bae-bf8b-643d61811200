/*
 * @Author: Strayer
 * @Date: 2025-08-06
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-06
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\api\core\energy-management\consume-statistic.ts
 */

import { requestClient } from '#/api/request';

/**
 * PriceCountTotalDTO
 */
export type PriceCountTotalDTO = {
  /**
   * 能耗曲线图列表
   */
  priceCountChartList?: PriceCountChartDTO[];
  /**
   * 能耗数据列表
   */
  priceCountList?: PriceCountDTO[];
};

/**
 * 能耗曲线图列表
 *
 * PriceCountChartDTO
 */
export type PriceCountChartDTO = {
  /**
   * x轴坐标，时间（精确到小时）
   */
  date?: string;
  /**
   * 用量
   */
  usage?: number;
};

/**
 * 能耗数据列表
 *
 * PriceCountDTO
 */
export type PriceCountDTO = {
  /**
   * 区域名称
   */
  areaName?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 记录结束时间（精确到小时）
   */
  endTime?: string;
  /**
   * 费用
   */
  price?: number;
  /**
   * 记录开始时间（精确到小时）
   */
  startTime?: string;
  /**
   * 用量
   */
  usage?: number;
};

/** 获取能耗数据列表 */
export async function getConsumeStatisticListApi(param: any) {
  return requestClient.get<PriceCountTotalDTO>(
    '/energyConsumptionManagement/priceCount',
    {
      params: param,
    },
  );
}
