<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { EnPeriodConfig } from '#/api/core/energy-management/period-config';
import type { EnPeriodType } from '#/api/core/energy-management/period-type';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, onUnmounted, ref, shallowRef, useTemplateRef } from 'vue';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';
import dayjs from 'dayjs';
import * as echarts from 'echarts';

import {
  addPeriodConfigApi,
  deletePeriodConfigApi,
  editPeriodConfigApi,
  getPeriodConfigListApi,
} from '#/api/core/energy-management/period-config';
import { getPeriodTypeListApi } from '#/api/core/energy-management/period-type';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const columnsScheme: ShallowRef<CustomColumnSchema<EnPeriodConfig>> =
  shallowRef(getColumnsScheme([]));
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(
  getFormOption([]),
);

const periodTypes = ref<EnPeriodType[]>([]);
getPeriodTypeListApi({}).then((res) => {
  periodTypes.value = res;
  formOption.value = getFormOption(res);
  columnsScheme.value = getColumnsScheme(periodTypes.value);
});

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<EnPeriodConfig>,
  fromData: any,
) {
  const res = await getPeriodConfigListApi(fromData);
  initChart(res.filter((item) => item.status === 1));
  return {
    items: res,
    total: res.length,
  };
}

const chart = ref<echarts.ECharts | null>(null);

function initChart(data: EnPeriodConfig[]) {
  if (periodTypes.value.length === 0) {
    setTimeout(() => {
      initChart(data);
    }, 500);
    return;
  }

  const chartContainer = document.querySelector(
    '#period-config-chart',
  ) as HTMLElement;
  if (!chartContainer) return;

  // 如果图表实例已存在且未销毁，先销毁它
  if (chart.value && !chart.value.isDisposed()) {
    chart.value.dispose();
  }

  // 创建新的图表实例
  chart.value = echarts.init(chartContainer);

  // 生成24小时的时间轴
  const hours = Array.from(
    { length: 24 },
    (_, i) => `${i.toString().padStart(2, '0')}:00`,
  );

  // 初始化24小时的时段分布数据
  const hourData = Array.from({ length: 24 }, () => ({
    value: 0,
    itemStyle: { color: '#f0f0f0' }, // 默认灰色
  }));

  // 处理时段配置数据
  data.forEach((config) => {
    if (config.status !== 1) return;

    const startHour = dayjs(config.startTime).hour();
    const endHour = dayjs(config.endTime).hour();

    // 找到对应的时段类型
    const periodTypeIndex = periodTypes.value.findIndex(
      (pt: EnPeriodType) => pt.periodTypeId === config.periodTypeId,
    );

    const periodType = periodTypes.value[periodTypeIndex];
    const periodTypeValue = periodTypes.value.length - periodTypeIndex;

    if (periodType) {
      // 处理跨天的情况
      if (startHour <= endHour) {
        // 同一天内
        for (let hour = startHour; hour < endHour; hour++) {
          if (hour >= 0 && hour < 24) {
            hourData[hour] = {
              value: periodTypeValue,
              itemStyle: { color: periodType.periodColor },
            };
          }
        }
      } else {
        // 跨天情况（如22:00-07:00）
        for (let hour = startHour; hour < 24; hour++) {
          hourData[hour] = {
            value: periodTypeValue,
            itemStyle: { color: periodType.periodColor },
          };
        }
        for (let hour = 0; hour < endHour; hour++) {
          hourData[hour] = {
            value: periodTypeValue,
            itemStyle: { color: periodType.periodColor },
          };
        }
      }
    }
  });

  const option = {
    title: {
      text: '时段分布 (24小时)',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLabel: {
        interval: 0,
        rotate: 0,
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    // 隐藏 Y 轴
    yAxis: {
      show: false,
      type: 'value',
    },
    series: [
      {
        name: '时段分布',
        type: 'bar',
        data: hourData,
        barWidth: '60%',
        itemStyle: {
          borderRadius: [2, 2, 0, 0],
        },
      },
    ],
  };

  chart.value.setOption(option);
}

// 组件卸载时清理资源
onUnmounted(() => {
  chart.value?.dispose();
  chart.value = null;
});

/**
 * @description: 删除
 */
function deletePeriodConfig(row: EnPeriodConfig) {
  return deletePeriodConfigApi(row.periodId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  return param.meta.type === 'create'
    ? addPeriodConfigApi(param.formData)
        .then(() => {
          message.success('新增成功');
          gridFormRef.value?.gridApi.reload();
          return true;
        })
        .catch(() => false)
    : editPeriodConfigApi({
        ...param.raw,
        ...param.formData,
      })
        .then(() => {
          message.success('更新成功');
          gridFormRef.value?.gridApi.query();
          return true;
        })
        .catch(() => false);
}
</script>

<template>
  <div class="h-full p-4">
    <div class="flex h-full flex-col bg-white">
      <GridForm
        ref="gridFormRef"
        class="flex-1"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :disable-page="true"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '时段配置信息',
                meta: { type: 'create' },
              })
            "
          />
        </template>
        <template #action="{ row }">
          <Button
            type="link"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '时段配置信息',
                formData: row,
                meta: { type: 'edit' },
              })
            "
          >
            编辑
          </Button>
          <RemoveButton @confirm="deletePeriodConfig(row)" />
        </template>
      </GridForm>
      <div id="period-config-chart" class="w-full flex-1 p-4"></div>
    </div>
  </div>
</template>
