<script setup lang="ts">
import { onMounted, shallowRef } from 'vue';

import { Graph } from '@antv/x6';

import { bindKey, createGraph, registerCustomShape, usePlugin } from './graph';
import { initStencil } from './stencil';

onMounted(() => {
  initFlowSheet();
});

const graph = shallowRef<Graph>();
function initFlowSheet() {
  // 初始化画布
  graph.value = createGraph();
  // 注册插件
  usePlugin(graph.value);

  // 快捷键与事件
  bindKey(graph.value);

  // 注册自定义图形/模块
  registerCustomShape();

  // 初始化 物料面板 stencil
  initStencil(graph.value);
}

function exportData() {
  return graph.value?.toJSON().cells ?? [];
}

function setData(data: any) {
  graph.value?.fromJSON(data);
}

defineExpose({
  exportData,
  setData,
});
</script>

<template>
  <div class="emergency-plan-manage__flowsheet relative">
    <div id="emergency-plan-manage-flowsheet" class="h-full w-full"></div>
    <div id="stencil" class="z-1000 absolute left-0 top-0 h-full w-60"></div>
  </div>
</template>
<style lang="scss">
.emergency-plan-manage__flowsheet {
  .x6-widget-stencil {
    background-color: #fff;
  }

  .x6-widget-stencil-title {
    background-color: #fff;
  }

  .x6-widget-stencil-group-title {
    background-color: #fff !important;
  }

  .x6-widget-transform {
    padding: 0;
    margin: -1px 0 0 -1px;
    border: 1px solid #239edd;
  }

  .x6-widget-transform > div {
    border: 1px solid #239edd;
  }

  .x6-widget-transform > div:hover {
    background-color: #3dafe4;
  }

  .x6-widget-transform-active-handle {
    background-color: #3dafe4;
  }

  .x6-widget-transform-resize {
    border-radius: 0;
  }

  .x6-widget-selection-inner {
    border: 1px solid #239edd;
  }

  .x6-widget-selection-box {
    opacity: 0;
  }
}
</style>
