<script lang="ts" setup>
import type { ShallowRef } from 'vue';
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CmDutyLog } from '#/api/core/emergency/duty-log';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addDutyLogApi,
  deleteDutyLogApi,
  editDutyLogApi,
  getDutyLogDetailApi,
  getDutyLogListApi,
} from '#/api/core/emergency/duty-log';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';
import { downloadFile } from '#/utils/comm';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<CmDutyLog>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<CmDutyLog>,
  fromData: any,
) {
  if (fromData.dateRange) {
    fromData.beginDate = fromData.dateRange[0].startOf('day').toISOString();
    fromData.endDate = fromData.dateRange[1].endOf('day').toISOString();
    delete fromData.dateRange;
  }

  return getDutyLogListApi(param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: CmDutyLog) {
  return deleteDutyLogApi(row.logId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: CmDutyLog) {
  gridFormRef.value?.gridApi.setLoading(true);
  getDutyLogDetailApi(row.logId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '值班日志',
        formData: {
          ...res,
          logAttachment: res.logAttachment?.filePath,
        },
        meta: { type: 'edit', raw: row },
      });
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: CmDutyLog;
}): Promise<boolean> {
  param.formData.logAttachment = {
    filePath: param.formData.logAttachment,
    logId: param.meta.raw?.logId,
  };

  if (param.meta.type === 'create')
    addDutyLogApi(param.formData)
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editDutyLogApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="日志列表"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增值班日志',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }: { row: CmDutyLog }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <!-- 下载 -->
          <Button
            type="link"
            @click="
              downloadFile(row.logAttachment?.filePath ?? '', row.title ?? '')
            "
          >
            下载
          </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
