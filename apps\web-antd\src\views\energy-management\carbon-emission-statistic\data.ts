import type { CarbonEmissionStatistic } from '#/api/core/energy-management/carbon-emission-statistic';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-18
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\energy-management\carbon-emission-statistic\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import dayjs from 'dayjs';

// 状态map
export const StatusMap: Record<
  number,
  { color: string; label: string; value: number }
> = {
  0: {
    value: 0,
    label: '待审核',
    color: 'orange',
  },
  1: {
    value: 1,
    label: '已审核',
    color: 'green',
  },
};

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      label: '日期范围',
      fieldName: 'dateRange',
      component: 'RangePicker',
      componentProps: {
        picker: 'month',
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CarbonEmissionStatistic> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'name',
      title: '名称',
    },
    {
      field: 'date',
      title: '计算年月',
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM') : '';
      },
    },
    {
      field: 'emission',
      title: '碳排放量(吨)',
    },
    {
      field: 'status',
      title: '状态',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.status;
        return [StatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '名称',
      rules: 'required',
    },
    {
      fieldName: 'date',
      label: '计算年月',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        picker: 'month',
      },
    },
  ];
}

export function getFormOptionByEdit(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '名称',
      rules: 'required',
    },
    {
      fieldName: 'date',
      label: '计算年月',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        picker: 'month',
      },
    },
    {
      fieldName: 'emission',
      label: '碳排放量(吨)',
      rules: 'required',
      component: 'InputNumber',
    },
  ];
}
