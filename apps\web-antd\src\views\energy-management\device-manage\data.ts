import type { EnDevice } from '#/api/core/energy-management/device-manage';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-05
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\energy-management\device-manage\data.ts
 */
import { getAreaListApi } from '#/api/core/energy-management/area-manage';

// 设备状态map
export const DeviceStatusMap: Record<
  number,
  { color: string; label: string; value: number }
> = {
  1: {
    value: 1,
    label: '正常',
    color: 'green',
  },
  2: {
    value: 2,
    label: '需维护',
    color: 'orange',
  },
  3: {
    value: 3,
    label: '异常',
    color: 'red',
  },
  4: {
    value: 4,
    label: '离线',
    color: 'gray',
  },
};

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'deviceName',
      label: '设备名称',
    },
    {
      fieldName: 'deviceCode',
      label: '设备编号',
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        options: Object.values(DeviceStatusMap),
      },
    },
    // 区域
    {
      fieldName: 'areaId',
      label: '区域',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getAreaListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              areaName: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.areaName ?? '',
            value: item.areaId,
          }));
        },
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EnDevice> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'deviceCode',
      title: '设备编号',
    },
    {
      field: 'deviceName',
      title: '设备名称',
    },
    // 状态
    {
      field: 'status',
      title: '状态',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.status;
        return [DeviceStatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'deviceCode',
      label: '设备编号',
      rules: 'required',
    },
    {
      fieldName: 'deviceName',
      label: '设备名称',
      rules: 'required',
    },
    // 区域
    {
      fieldName: 'areaId',
      label: '区域',
      rules: 'required',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getAreaListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              areaName: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.areaName ?? '',
            value: item.areaId,
          }));
        },
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(DeviceStatusMap),
      },
    },
  ];
}
