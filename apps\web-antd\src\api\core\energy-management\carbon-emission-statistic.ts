/*
 * @Author: Strayer
 * @Date: 2025-08-18
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-19
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\api\core\energy-management\carbon-emission-statistic.ts
 */

import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * 碳排放统计
 */
export type CarbonEmissionStatistic = {
  /**
   * 创建人
   */
  createName?: string;
  /**
   * 创建时间（自动记录）
   */
  createTime?: string;
  /**
   * 所需计算的年月
   */
  date?: string;
  /**
   * 碳排放量
   */
  emission?: number;
  /**
   * 主键ID（自动递增）
   */
  id: number;
  /**
   * 名称-copy  注：nodeName会导致弹窗bug,所以签到转为name来使用
   */
  name?: string;
  /**
   * 名称
   */
  nodeName?: string;
  /**
   * 状态(1:已审核 0:待审核)
   */
  status?: number;
  /**
   * 更新人
   */
  updateName?: string;
  /**
   * 修改时间（自动更新）
   */
  updateTime?: string;
};

/**
 * 碳排放计算单详情数据
 *
 * CalcOrderDTO
 */
export type CalcOrderDTO = {
  /**
   * 设备唯一标识符
   */
  deviceId?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 维度（1:用水 2:用电）
   */
  metrics?: number;
  /**
   * 节点指标
   */
  nodeData?: number;
  /**
   * 设备用量
   */
  value?: number;
};

/** 获取碳排放统计列表 */
export async function getCarbonEmissionStatisticListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage
    .get<{
      items: CarbonEmissionStatistic[];
      total: number;
    }>('/carbon/calcOrder/page', {
      params: {
        pageNumber: param.currentPage,
        pageSize: param.pageSize,
        ...search,
      },
    })
    .then((res) => {
      res.items = res.items.map((item) => {
        item.name = item.nodeName;
        return item;
      });
      return res;
    });
}

/** 获取碳排放统计详情 */
export async function getCarbonEmissionStatisticDetailApi(id: number) {
  return requestClient.get<CalcOrderDTO[]>(`/carbon/calcOrder/detail/${id}`);
}

/** 新增碳排放统计 */
export async function addCarbonEmissionStatisticApi(
  data: CarbonEmissionStatistic,
) {
  data.nodeName = data.name;
  return requestClient.post<CarbonEmissionStatistic>('/carbon/calcOrder', data);
}

/** 修改碳排放统计 */
export async function editCarbonEmissionStatisticApi(
  data: CarbonEmissionStatistic,
) {
  data.nodeName = data.name;
  return requestClient.put<CarbonEmissionStatistic>('/carbon/calcOrder', data);
}

/** 删除碳排放统计 */
export async function deleteCarbonEmissionStatisticApi(id: number) {
  return requestClient.delete<string>(`/carbon/calcOrder/${id}`);
}
