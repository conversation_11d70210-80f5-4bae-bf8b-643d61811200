import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * CmEmergencyPlans
 */
export type CmEmergencyPlans = {
  /**
   * 对可能发生事故的详细描述
   */
  accidentDesc?: string;
  /**
   * 事故类型分类
   */
  accidentType?: string;
  /**
   * 应急预案流程图关系
   */
  cmEmergencyPlanFlowRelationsList?: CmEmergencyPlanFlowRelations[];
  /**
   * 预案措施参数
   */
  cmEmergencyPlanParamConfigList?: CmEmergencyPlanParamConfig[];
  /**
   * 负责人联系电话
   */
  contactPhone?: string;
  /**
   * 创建该预案的用户
   */
  creater?: string;
  /**
   * 编制时间
   */
  createTime?: string;
  /**
   * 编制该预案的部门
   */
  department?: string;
  /**
   * 流程图
   */
  flow?: string;
  /**
   * 最后修改该预案的用户
   */
  modifier?: string;
  /**
   * 修改时间
   */
  modifyTime?: string;
  /**
   * 系统自动生成的预案编号
   */
  planCode?: string;
  /**
   * 预案唯一标识
   */
  planId: string;
  /**
   * 预案等级(特别重大、重大等)
   */
  planLevel?: string;
  /**
   * 应急预案的名称
   */
  planName?: string;
  /**
   * 预案负责人姓名
   */
  responsibility?: string;
  /**
   * 预案适用的范围
   */
  scope?: string;
  /**
   * 状态
   */
  status?: string;
  /**
   * 报告的版本号
   */
  version?: string;
};

/**
 * CmEmergencyPlanFlowRelations对象
 *
 * CmEmergencyPlanFlowRelations
 */
export type CmEmergencyPlanFlowRelations = {
  /**
   * 子模块ID
   */
  childModuleId?: string;
  /**
   * 执行该关系的条件表达式
   */
  conditionExpr?: string;
  /**
   * 关系创建时间
   */
  createTime?: string;
  /**
   * 创建该关系的用户
   */
  creator?: string;
  /**
   * 模块执行顺序
   */
  orderNo?: number;
  /**
   * 父模块ID(可为空，表示起始节点)
   */
  parentModuleId?: string;
  /**
   * 关联的预案ID
   */
  planId: string;
  /**
   * 流程图关系唯一标识
   */
  relationId: string;
};

/**
 * CmEmergencyPlanParamConfig对象
 *
 * CmEmergencyPlanParamConfig
 */
export type CmEmergencyPlanParamConfig = {
  /**
   * 参数创建时间
   */
  createTime?: string;
  /**
   * 创建该参数的用户
   */
  creator?: string;
  /**
   * 是否必填(true-是，false-否)
   */
  isRequired?: boolean;
  /**
   * 最后修改该参数的用户
   */
  modifier?: string;
  /**
   * 参数最后修改时间
   */
  modifyTime?: string;
  /**
   * 关联的模块ID
   */
  moduleId?: string;
  /**
   * 参数代码
   */
  paramCode?: string;
  /**
   * 参数描述
   */
  paramDesc?: string;
  /**
   * 参数唯一标识
   */
  paramId: string;
  /**
   * 参数名称
   */
  paramName?: string;
  /**
   * 参数类型(文本、数字、日期等)
   */
  paramType?: string;
  /**
   * 参数值
   */
  paramValue?: string;
  /**
   * 关联的预案ID
   */
  planId: string;
  /**
   * 参数单位
   */
  unit?: string;
};

/** 获取应急预案列表 */
export async function getEmergencyPlanListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: CmEmergencyPlans[];
    total: number;
  }>('/cm-emergency-plans', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 获取应急预案详情 */
export async function getEmergencyPlanDetailApi(planId: string) {
  return requestClient.get<CmEmergencyPlans>(
    `/cm-emergency-plans/detail/${planId}`,
  );
}

/** 新增应急预案 */
export async function addEmergencyPlanApi(data: CmEmergencyPlans) {
  return requestClient.post<CmEmergencyPlans>('/cm-emergency-plans', data);
}

/** 修改应急预案 */
export async function editEmergencyPlanApi(data: CmEmergencyPlans) {
  return requestClient.put<CmEmergencyPlans>('/cm-emergency-plans', data);
}

/** 删除应急预案 */
export async function deleteEmergencyPlanApi(planId: string) {
  return requestClient.delete<CmEmergencyPlans>(
    `/cm-emergency-plans/${planId}`,
  );
}

/** 修改状态 */
export async function updateEmergencyPlanStatusApi(
  planId: string,
  status: string,
) {
  return requestClient.put<CmEmergencyPlans>(
    `/cm-emergency-plans/${planId}/${status}`,
  );
}

/** 统计 */
export async function getEmergencyPlanCountApi() {
  return requestClient.post<{
    totalCount: number;
    typeMap: Record<string, number>;
  }>('/cm-emergency-plans/statistics');
}
