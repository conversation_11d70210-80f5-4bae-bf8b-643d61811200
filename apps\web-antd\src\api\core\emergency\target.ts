import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * CmProtectionTarget对象
 */
export type EmergencyTarget = {
  /**
   * 防护单位详细地址
   */
  address?: string;
  /**
   * 目标类别：工业设施/居民区/公共设施/环境敏感区/其他
   */
  category?: string;
  /**
   * 照片
   */
  cmProtectionTargetPhoto?: CmProtectionTargetPhoto;
  /**
   * 防护目标应急资源需求
   */
  cmProtectionTargetResources?: CmProtectionTargetResources;
  /**
   * 责任人联系电话
   */
  contact?: string;
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 防护目标的基本情况描述
   */
  description?: string;
  /**
   * 与危险源的距离(km)
   */
  distance?: number;
  /**
   * 防护单位地理位置纬度
   */
  latitude?: number;
  /**
   * 防护单位地理位置经度
   */
  longitude?: number;
  /**
   * 防护单位的正式名称
   */
  name?: string;
  /**
   * 防护单位内人员数量
   */
  population?: number;
  /**
   * 防护级别：一级防护/二级防护/三级防护/四级防护
   */
  protectionLevel?: string;
  /**
   * 防护单位负责人姓名
   */
  responsible?: string;
  /**
   * 风险等级：高风险/中风险/低风险
   */
  riskLevel?: string;
  /**
   * 防护目标唯一标识
   */
  targetId: string;
  /**
   * 记录更新时间（自动更新）
   */
  updateTime?: string;
};

/**
 * 照片
 *
 * CmProtectionTargetPhoto
 */
export type CmProtectionTargetPhoto = {
  /**
   * 照片简要描述（最大长度100字符）
   */
  description?: string;
  /**
   * 照片唯一标识（主键）
   */
  photoId: string;
  /**
   * 照片存储路径
   */
  photoPath?: string;
  /**
   * 照片显示顺序（默认0）
   */
  sortOrder?: number;
  /**
   * 关联防护目标基本信息表（外键）
   */
  targetId: string;
  /**
   * 照片上传时间（自动记录）
   */
  uploadTime?: string;
};

/**
 * 防护目标应急资源需求
 *
 * CmProtectionTargetResources
 */
export type CmProtectionTargetResources = {
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 疏散资源需求（不需要/少量/中等/大量）
   */
  evacuationResource?: string;
  /**
   * 消防资源需求（不需要/少量/中等/大量）
   */
  fireResource?: string;
  /**
   * 医疗资源需求（不需要/少量/中等/大量）
   */
  medicalResource?: string;
  /**
   * 其他应急资源需求描述
   */
  otherResources?: string;
  /**
   * 记录唯一标识（主键）
   */
  recordId: string;
  /**
   * 关联防护目标基本信息表（外键）
   */
  targetId: string;
};

/**
 * 获取应急防护目标列表
 */
export async function getEmergencyTargetListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: EmergencyTarget[];
    total: number;
  }>('/cm-protection-target', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/**
 * 获取应急防护目标详情
 */
export async function getEmergencyTargetDetailApi(targetId: string) {
  return requestClient.get<EmergencyTarget>(
    `/cm-protection-target/detail/${targetId}`,
  );
}

/**
 * 新增应急防护目标
 */
export async function addEmergencyTargetApi(data: EmergencyTarget) {
  return requestClient.post<EmergencyTarget>('/cm-protection-target', data);
}

/**
 * 修改应急防护目标
 */
export async function editEmergencyTargetApi(data: EmergencyTarget) {
  return requestClient.put<EmergencyTarget>('/cm-protection-target', data);
}

/**
 * 删除应急防护目标
 */
export async function deleteEmergencyTargetApi(targetId: string) {
  return requestClient.delete<EmergencyTarget>(
    `/cm-protection-target/${targetId}`,
  );
}
