import type { CmPersonnel } from './organization';

import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * CmDutyLog对象
 *
 * CmDutyLog
 */
export type CmDutyLog = {
  /**
   * 记录创建时间
   */
  createTime?: string;
  /**
   * 日志详细描述
   */
  description?: string;
  /**
   * 值班附件
   */
  logAttachment?: CmLogAttachment;
  /**
   * 日志唯一标识
   */
  logId: number;
  /**
   * 日志类型(1-工作事项,2-问题处理,3-交接情况,4-其他)
   */
  logType?: number;
  /**
   * 问题处理措施
   */
  measures?: string;
  /**
   * 员工信息
   */
  personnel?: CmPersonnel;
  /**
   * 日志记录时间
   */
  recordTime?: string;
  /**
   * 关联员工表的值班人员ID
   */
  staffId?: number;
  /**
   * 状态(1-待处理,2-已完成,3-已忽略)
   */
  status?: number;
  /**
   * 日志标题
   */
  title?: string;
  /**
   * 记录更新时间
   */
  updateTime?: string;
};

/**
 * 值班附件
 *
 * CmLogAttachment
 */
export type CmLogAttachment = {
  /**
   * 附件唯一标识（主键）
   */
  attachmentId: number;
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 附件文件名
   */
  fileName?: string;
  /**
   * 文件存储路径
   */
  filePath?: string;
  /**
   * 文件大小（字节）（默认0）
   */
  fileSize?: number;
  /**
   * 文件类型（如：png、jpg、pdf等）
   */
  fileType?: string;
  /**
   * 关联日志主表ID
   */
  logId: number;
};

/** 日志类型 */
export const DutyLogTypeMap: Record<number, { label: string; value: number }> =
  {
    1: {
      label: '工作事项',
      value: 1,
    },
    2: {
      label: '问题处理',
      value: 2,
    },
    3: {
      label: '交接情况',
      value: 3,
    },
    4: {
      label: '其他',
      value: 4,
    },
  };

/** 日志状态 */
export const DutyLogStatusMap: Record<
  number,
  { label: string; value: number }
> = {
  1: {
    label: '待处理',
    value: 1,
  },
  2: {
    label: '已完成',
    value: 2,
  },
  3: {
    label: '已忽略',
    value: 3,
  },
};

/** 获取值班日志列表 */
export async function getDutyLogListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: CmDutyLog[];
    total: number;
  }>('/cm-duty-log', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 获取值班日志详情 */
export async function getDutyLogDetailApi(logId: number) {
  return requestClient.get<CmDutyLog>(`/cm-duty-log/detail/${logId}`);
}

/** 新增值班日志 */
export async function addDutyLogApi(data: CmDutyLog) {
  return requestClient.post<CmDutyLog>('/cm-duty-log', data);
}

/** 修改值班日志 */
export async function editDutyLogApi(data: CmDutyLog) {
  return requestClient.put<CmDutyLog>('/cm-duty-log', data);
}

/** 删除值班日志 */
export async function deleteDutyLogApi(logId: number) {
  return requestClient.delete<CmDutyLog>(`/cm-duty-log/${logId}`);
}
