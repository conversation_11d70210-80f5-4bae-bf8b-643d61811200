<script setup lang="ts">
import type {
  CarbonEmissionPlan,
  CarbonEmissionPlanDetail,
} from '#/api/core/energy-management/carbon-emission-plan';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import {
  Button,
  DatePicker,
  InputNumber,
  message,
  Table,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addCarbonEmissionPlanApi,
  editCarbonEmissionPlanApi,
} from '#/api/core/energy-management/carbon-emission-plan';

const emit = defineEmits<{
  (e: 'confirm'): void;
}>();

const date = ref(dayjs().startOf('year'));

const title = ref('');

const type = ref<'create' | 'edit'>('create');

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    console.info('onConfirm');
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      init();
    }
  },
});

function init() {
  const data = modalApi.getData<Record<string, any>>();
  title.value = data.title;
  type.value = data.type;

  if (data.formData) {
    form.value = data.formData;

    yearData.value[0]!.value = form.value.targetEmission ?? undefined;
    yearData.value[1]!.value = form.value.actualEmission ?? undefined;

    if (type.value === 'edit') {
      monthData.value = data.formData.detailList?.length
        ? data.formData.detailList.map((item: CarbonEmissionPlanDetail) => ({
            month: item.month,
            plannedEmission: item.plannedEmission,
            actualEmission: item.actualEmission,
          }))
        : [];
    }
  } else {
    yearData.value[0]!.value = undefined;
    yearData.value[1]!.value = undefined;
    monthData.value = [];
  }
}

const form = ref<Partial<CarbonEmissionPlan>>({
  year: undefined,
  targetEmission: undefined,
  actualEmission: undefined,
  detailList: [],
});

const yearColumns = [
  {
    title: '年度碳排放概览',
    dataIndex: 'label',
    key: 'label',
    colSpan: 2,
    width: '50%',
  },
  {
    title: '',
    dataIndex: 'value',
    key: 'value',
    colSpan: 0,
    width: '50%',
  },
];

const yearData = ref<{ label: string; value: number | undefined }[]>([
  {
    label: '目标排放量(吨)',
    value: undefined,
  },
  {
    label: '实际排放量(吨)',
    value: undefined,
  },
]);

const monthColumns = [
  {
    title: '月份',
    dataIndex: 'month',
    key: 'month',
    width: '20%',
  },
  {
    title: '计划排放量(吨)',
    dataIndex: 'plannedEmission',
    key: 'plannedEmission',
    width: '40%',
  },
  {
    title: '实际排放量(吨)',
    dataIndex: 'actualEmission',
    key: 'actualEmission',
    width: '40%',
  },
];

const monthData = ref<Partial<CarbonEmissionPlanDetail>[]>([]);

async function handleConfirm() {
  if (!yearData.value[0]!.value) {
    message.error('请输入年度目标排放量');
    return;
  }

  if (type.value === 'edit') {
    if (
      monthData.value.some(
        (each) => !each.plannedEmission && each.plannedEmission !== 0,
      )
    ) {
      message.error('请输入月度计划排放量');
      return;
    }

    const monthPlanSummary = monthData.value.reduce(
      (acc, cur) => acc + (cur.plannedEmission ?? 0),
      0,
    );

    if (monthPlanSummary !== yearData.value[0]!.value) {
      message.error('月度计划排放量之和与年度目标排放量不一致');
      return;
    }
  }

  const reqData = cloneDeep(form.value);
  reqData.targetEmission = yearData.value[0]!.value;
  reqData.actualEmission = yearData.value[1]!.value;
  reqData.year = date.value.year();

  if (reqData.detailList?.length) {
    monthData.value.forEach((each, index) => {
      reqData.detailList![index]!.plannedEmission = each.plannedEmission ?? 0;
      reqData.detailList![index]!.actualEmission = each.actualEmission ?? 0;
    });
  } else {
    reqData.detailList = monthData.value.map((each) => ({
      month: each.month!,
      plannedEmission: each.plannedEmission ?? 0,
      actualEmission: each.actualEmission ?? 0,
    })) as any;
  }

  const api =
    type.value === 'create'
      ? addCarbonEmissionPlanApi
      : editCarbonEmissionPlanApi;

  await api(reqData as any);
  modalApi.close();
  message.success('保存成功');
  emit('confirm');
}

function handleCancel() {
  modalApi.close();
}
</script>

<template>
  <Modal
    class="w-[600px]"
    :title="title"
    :show-cancel-button="false"
    :show-confirm-button="false"
    :width="800"
  >
    <div class="mb-4">
      <span>选择时间：</span>
      <DatePicker v-model:value="date" picker="year" />
    </div>

    <!-- 年表格 -->
    <Table
      class="mb-4 rounded-none"
      bordered
      :columns="yearColumns"
      :data-source="yearData"
      :pagination="false"
      size="small"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'label'">
          {{ record.label }}
        </template>
        <template v-else-if="column.key === 'value'">
          <InputNumber
            class="w-4/5"
            v-model:value="record.value"
            :min="0"
            :disabled="index > 0"
          />
        </template>
      </template>
    </Table>

    <!-- 月表格 -->
    <Table
      v-if="type === 'edit'"
      class="rounded-none"
      bordered
      :columns="monthColumns"
      :data-source="monthData"
      :pagination="false"
      size="small"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'month'">
          {{ record.month }}
        </template>
        <template v-else-if="column.key === 'plannedEmission'">
          <InputNumber
            class="w-full"
            v-model:value="record.plannedEmission"
            :min="0"
          />
        </template>
        <template v-else-if="column.key === 'actualEmission'">
          <InputNumber
            class="w-full"
            v-model:value="record.actualEmission"
            :min="0"
            :disabled="true"
          />
        </template>
      </template>
    </Table>

    <template #footer>
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" @click="handleConfirm">保存</Button>
    </template>
  </Modal>
</template>
