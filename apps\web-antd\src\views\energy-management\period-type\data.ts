import type { EnPeriodType } from '#/api/core/energy-management/period-type';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
// export function getTopSearchScheme(): CustomFormSchema[] {
//   return [
//     {
//       fieldName: 'periodName',
//       label: '时段名称',
//     },
//     {
//       fieldName: 'periodColor',
//       label: '颜色代码',
//     },
//   ];
// }

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EnPeriodType> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'periodTypeId', title: 'ID', visible: false },
    // 时段名称
    {
      field: 'periodName',
      title: '时段名称',
      customType: 'tag',
      formatter: ((params: any) => {
        return [
          {
            label: params.row.periodName,
            color: params.row.periodColor,
          },
        ];
      }) as any,
    },
    // 颜色代码
    { field: 'periodColor', title: '颜色' },
    // 描述
    { field: 'description', title: '描述' },
    // 排序
    { field: 'sortOrder', title: '排序' },
    {
      field: 'action',
      title: '操作',
      width: 180,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'periodName',
      label: '时段名称',
      rules: 'required',
    },
    {
      fieldName: 'periodColor',
      label: '颜色代码',
      rules: 'required',
      component: 'ColorPicker',
    },
    {
      fieldName: 'description',
      label: '描述',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入时段描述信息',
      },
    },
    {
      fieldName: 'sortOrder',
      label: '排序',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入显示排序',
        min: 0,
      },
    },
  ];
}
