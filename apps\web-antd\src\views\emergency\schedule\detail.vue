<script setup lang="ts">
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { CmSchedule } from '#/api/core/emergency/schedule';
import type { CustomFormSchema } from '#/components/form-modal';

import { shallowRef, useTemplateRef } from 'vue';

import { Button, message, Popconfirm } from 'ant-design-vue';
import { clone } from 'remeda';

import {
  deleteEmergencyScheduleApi,
  editEmergencyScheduleApi,
} from '#/api/core/emergency/schedule';
import FormModal from '#/components/form-modal/index.vue';

import { shiftMap } from './data';

const emit = defineEmits<{
  (e: 'hadEdit'): void;
}>();

const scheduleObj = shallowRef<CmSchedule>();

// 新建或编辑表单弹窗
const formModalRef =
  useTemplateRef<ComponentExposed<typeof FormModal>>('formModalRef');

const formOption: CustomFormSchema[] = [
  {
    fieldName: 'name',
    label: '员工',
    rules: 'required',
    disabled: true,
  },
  {
    fieldName: 'positionId',
    label: '岗位',
    rules: 'required',
    disabled: true,
  },
  {
    fieldName: 'shiftId',
    label: '班次',
    rules: 'required',
    component: 'Select',
    componentProps: {
      options: Object.values(shiftMap).map((item) => ({
        label: item.label,
        value: item.label,
      })),
    },
  },
  {
    fieldName: 'scheduleDate',
    label: '排班日期',
    rules: 'required',
    component: 'DatePicker',
  },
  {
    fieldName: 'remark',
    label: '备注',
    component: 'Textarea',
  },
];

/**
 * @description: 编辑
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  Reflect.deleteProperty(param.formData, 'name');

  editEmergencyScheduleApi({
    ...param.meta.raw,
    ...param.formData,
  })
    .then(() => {
      message.success('修改成功');
      emit('hadEdit');
      return true;
    })
    .catch(() => false);

  return Promise.resolve(true);
}

/** 删除 */
function deleteSchedule() {
  deleteEmergencyScheduleApi(scheduleObj.value?.scheduleId!)
    .then(() => {
      message.success('删除成功');
      formModalRef.value?.modalApi.close();
      emit('hadEdit');
    })
    .catch(() => false);
}

function open(param: CmSchedule) {
  scheduleObj.value = param;
  formModalRef.value?.open({
    title: '排班详情',
    formData: {
      ...param,
      name: param.personnel?.name ?? '',
    },
    meta: { type: 'edit', raw: clone(param) },
  });
}

defineExpose({
  open,
});
</script>

<template>
  <!-- 详情弹窗 -->
  <FormModal ref="formModalRef" :form-option="formOption" :submit="onSubmit">
    <template #center-footer>
      <Popconfirm title="是否确认删除?" @confirm="deleteSchedule()">
        <Button type="primary" danger>删除</Button>
      </Popconfirm>
    </template>
  </FormModal>
</template>
