import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * EmergencyExpert对象
 *
 */
export type EmergencyExpert = {
  /**
   * 专家年龄
   */
  age?: number;
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 专家详细描述
   */
  description?: string;
  /**
   * 电子邮箱地址
   */
  email?: string;
  /**
   * 专家业务编号（唯一）
   */
  expertCode?: string;
  /**
   * 专家唯一标识符（主键）
   */
  expertId: string;
  /**
   * 擅长处置类型代码
   */
  expertiseType?: string;
  /**
   * 专家级别代码
   */
  expertLevel?: string;
  /**
   * 专家性别（默认male）
   */
  gender?: string;
  /**
   * 专家姓名
   */
  name?: string;
  /**
   * 联系电话（手机号码）
   */
  phone?: string;
  /**
   * 专家照片存储路径
   */
  photoPath?: string;
  /**
   * 职务
   */
  position?: string;
  /**
   * 专业领域
   */
  specialty?: string;
  /**
   * 专家当前状态（默认active）
   */
  status?: string;
  /**
   * 职称
   */
  title?: string;
  /**
   * 记录更新时间（自动更新）
   */
  updateTime?: string;
  /**
   * 工作单位
   */
  workUnit?: string;
};

/**
 * 获取应急专家列表
 */
export async function getEmergencyExpertListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: EmergencyExpert[];
    total: number;
  }>('/cm-expert-info', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/**
 * 获取应急专家详情
 */
export async function getEmergencyExpertDetailApi(expertId: string) {
  return requestClient.get<EmergencyExpert>(
    `/cm-expert-info/detail/${expertId}`,
  );
}

/**
 * 新增应急专家
 */
export async function addEmergencyExpertApi(data: EmergencyExpert) {
  return requestClient.post<EmergencyExpert>('/cm-expert-info', data);
}

/**
 * 修改应急专家
 */
export async function editEmergencyExpertApi(data: EmergencyExpert) {
  return requestClient.put<EmergencyExpert>('/cm-expert-info', data);
}

/**
 * 删除应急专家
 */
export async function deleteEmergencyExpertApi(expertId: string) {
  return requestClient.delete<EmergencyExpert>(`/cm-expert-info/${expertId}`);
}
