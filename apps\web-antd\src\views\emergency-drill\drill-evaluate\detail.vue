<script setup lang="ts">
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { EvaluateProject } from './data';

import type { CmDrillBasicInfo } from '#/api/core/emergency-drill/drill-evaluate';
import type { CustomColumnSchema } from '#/components/grid-form';

import { shallowRef, useTemplateRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Card, Textarea } from 'ant-design-vue';
import dayjs from 'dayjs';

import GridForm from '#/components/grid-form/index.vue';

import { StatusMap } from './data';

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const equipment = shallowRef<CmDrillBasicInfo>();

const [Modal, modalApi] = useVbenModal({
  footer: false,
  onCancel() {
    modalApi.close();
    emit('close');
  },
});

function open(param: CmDrillBasicInfo) {
  equipment.value = param;
  modalApi.open();
}

defineExpose({
  open,
});
// 评估项目
const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const columnsScheme: CustomColumnSchema<EvaluateProject> = [
  { field: 'name', title: '评估项目' },
  { field: 'target', title: '评估指标' },
  { field: 'weight', title: '权重(%)' },
  { field: 'score', title: '评估分数' },
  { field: 'idea', title: '评估意见' },
];

/**
 * @description: 获取表格数据
 */
async function query() {
  const projectList: EvaluateProject[] = JSON.parse(
    equipment.value?.rating ?? '[]',
  );

  return {
    items: projectList || [],
    total: projectList?.length || 0,
  };
}
</script>

<template>
  <Modal class="w-[800px]" title="应急演练评估报告">
    <!-- 设备基本信息展示 -->
    <div v-if="equipment">
      <Card title="基本信息" size="small">
        <div class="grid grid-cols-2 gap-x-4 gap-y-1">
          <div>
            <label class="mr-3">演练标题：</label>
            {{ equipment.title }}
          </div>
          <div>
            <label class="mr-3">评估日期：</label>
            {{
              equipment.valuationTime
                ? dayjs(equipment.valuationTime).format('YYYY-MM-DD HH:mm:ss')
                : ''
            }}
          </div>
          <div>
            <label class="mr-3">演练类型：</label>
            {{ equipment.type }}
          </div>
          <!-- 评估负责人 -->
          <div>
            <label class="mr-3">评估负责人：</label>
            {{ equipment.person }}
          </div>
          <!-- 组织单位 -->
          <div>
            <label class="mr-3">组织单位：</label>
            {{ equipment.organizer }}
          </div>
          <!-- 评估部门 -->
          <div>
            <label class="mr-3">评估部门：</label>
            {{ equipment.department }}
          </div>
          <!-- 演练时间 -->
          <div>
            <label class="mr-3">演练时间：</label>
            {{
              equipment.drillTime
                ? dayjs(equipment.drillTime).format('YYYY-MM-DD HH:mm:ss')
                : ''
            }}
          </div>
          <!-- 评估结果 -->
          <div>
            <label class="mr-3">评估结果：</label>
            <span
              :style="{
                color:
                  StatusMap[equipment.evaluationResult ?? '']?.color ?? 'black',
              }"
            >
              {{ equipment.evaluationResult }}
            </span>
          </div>
          <!-- 演练地点 -->
          <div>
            <label class="mr-3">演练地点：</label>
            {{ equipment.location }}
          </div>
          <!-- 评估总分 -->
          <div>
            <label class="mr-3">评估总分：</label>
            <span class="text-2xl">{{ equipment.fraction }}</span>
            <span>分</span>
          </div>
        </div>
      </Card>
    </div>
    <!-- 检测报告表格 -->
    <div class="mt-8">
      <Card title="评估项目评分" size="small">
        <GridForm
          :grid-options="{
            toolbarConfig: undefined,
            height: 300,
            pagerConfig: { enabled: false },
          }"
          ref="gridFormRef"
          :columns-scheme="columnsScheme"
          :query="query"
          :disable-layout-page="true"
        />
      </Card>
    </div>
    <div class="mt-8">
      <Card title="总体评估" size="small">
        <div class="mt-2 text-base">评估总计</div>
        <Textarea
          class="mt-3 bg-[#f8fafc]"
          :value="equipment?.opinion"
          readonly
          autosize
        />
        <div class="mt-2 text-base">优点与成效</div>
        <Textarea
          class="mt-3 bg-[#f8fafc]"
          :value="equipment?.advantage"
          readonly
          autosize
        />
        <div class="mt-2 text-base">问题与不足</div>
        <Textarea
          class="mt-3 bg-[#f8fafc]"
          :value="equipment?.insufficient"
          readonly
          autosize
        />
        <div class="mt-2 text-base">改进建议</div>
        <Textarea
          class="mt-3 bg-[#f8fafc]"
          :value="equipment?.suggestion"
          readonly
          autosize
        />
      </Card>
    </div>
  </Modal>
</template>
<style lang="css" scoped>
label {
  color: oklch(44.6% 0.043 257.281deg);
}
</style>
