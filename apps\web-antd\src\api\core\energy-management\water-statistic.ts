import { requestClient } from '#/api/request';

/**
 * WaterUsageRecordListDTO
 */
export type WaterUsageRecordListDTO = {
  /**
   * 抬头
   */
  waterList?: WaterDTO[];
  /**
   * 用水计量分页列表
   */
  waterUsageRecordList?: PageWaterUsageRecordDTO;
};

/**
 * 用水计量抬头
 *
 * WaterDTO
 */
export type WaterDTO = {
  /**
   * 区域唯一标识符
   */
  areaId: string;
  /**
   * 区域名称：总用水量名称 为 总用水量
   */
  areaName?: string;
  /**
   * true 为上升， false 为下降
   */
  upOrDown?: boolean;
  /**
   * 用水量（单位:m³）
   */
  waterUsage?: number;
  /**
   * 同上周同一天对比百分比
   */
  waterUsageRatio?: number;
};

/**
 * 用水计量分页列表
 *
 * PageWaterUsageRecordDTO
 */
export type PageWaterUsageRecordDTO = {
  /**
   * 查询数据列表
   */
  records?: WaterUsageRecordDTO[];
  /**
   * 总数
   */
  total?: number;
};

/**
 * 用水计量
 *
 * WaterUsageRecordDTO
 */
export type WaterUsageRecordDTO = {
  /**
   * 区域编码
   */
  areaCode?: string;
  /**
   * 区域ID
   */
  areaId?: string;
  /**
   * 区域名称
   */
  areaName?: string;
  /**
   * 创建时间戳（自动记录）
   */
  createTime?: string;
  /**
   * 设备编号
   */
  deviceCode?: string;
  /**
   * 设备ID
   */
  deviceId?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 记录结束时间（精确到小时）
   */
  endTime?: string;
  /**
   * 记录日期
   */
  recordDate?: string;
  /**
   * 记录唯一标识符
   */
  recordId: string;
  /**
   * 记录开始时间（精确到小时）
   */
  startTime?: string;
  /**
   * 更新时间戳（自动更新）
   */
  updateTime?: string;
  /**
   * 用水量（单位:m³）
   */
  waterUsage?: number;
};

/**
 *
 * @returns 获取用水统计列表
 */
export async function getWaterStatisticListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClient.get<WaterUsageRecordListDTO>(
    '/energyConsumptionManagement/waterUsageRecord/page',
    {
      params: {
        pageNumber: param.currentPage,
        pageSize: param.pageSize,
        ...search,
      },
    },
  );
}

/** 新增用水统计 */
export async function addWaterStatisticApi(data: WaterUsageRecordDTO) {
  return requestClient.post<WaterUsageRecordDTO>(
    '/energyConsumptionManagement/waterUsageRecord',
    data,
  );
}

/** 修改用水统计 */
export async function editWaterStatisticApi(data: WaterUsageRecordDTO) {
  return requestClient.put<WaterUsageRecordDTO>(
    '/energyConsumptionManagement/waterUsageRecord',
    data,
  );
}

/** 删除用水统计 */
export async function deleteWaterStatisticApi(recordId: string) {
  return requestClient.delete<string>(
    `/energyConsumptionManagement/waterUsageRecord/${recordId}`,
  );
}
