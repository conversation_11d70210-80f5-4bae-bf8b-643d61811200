import { requestClient } from '#/api/request';

/**
 * FlowUsageRecordListDTO
 */
export type FlowUsageRecordListDTO = {
  /**
   * 抬头
   */
  flowList?: FlowDTO[];
  /**
   * 流量计量分页列表
   */
  flowUsageRecordList?: PageFlowUsageRecordDTO;
};

/**
 * 流量计量抬头
 *
 * WaterDTO
 */
export type FlowDTO = {
  /**
   * 区域唯一标识符
   */
  areaId: string;
  /**
   * 区域名称：总流量量名称 为 总流量量
   */
  areaName?: string;
  /**
   * 流量量（单位:m³）
   */
  flowUsage?: number;
  /**
   * 同上周同一天对比百分比
   */
  flowUsageRatio?: number;
  /**
   * true 为上升， false 为下降
   */
  upOrDown?: boolean;
};

/**
 * 流量计量分页列表
 *
 * PageWaterUsageRecordDTO
 */
export type PageFlowUsageRecordDTO = {
  /**
   * 查询数据列表
   */
  records?: FlowUsageRecordDTO[];
  /**
   * 总数
   */
  total?: number;
};

/**
 * 流量计量
 *
 * FlowUsageRecordDTO
 */
export type FlowUsageRecordDTO = {
  /**
   * 区域编码
   */
  areaCode?: string;
  /**
   * 区域ID
   */
  areaId?: string;
  /**
   * 区域名称
   */
  areaName?: string;
  /**
   * 创建时间戳（自动记录）
   */
  createTime?: string;
  /**
   * 设备编号
   */
  deviceCode?: string;
  /**
   * 设备ID
   */
  deviceId?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 记录结束时间（精确到小时）
   */
  endTime?: string;
  /**
   * 流量量（单位:m³）
   */
  flowUsage?: number;
  /**
   * 记录日期
   */
  recordDate?: string;
  /**
   * 记录唯一标识符
   */
  recordId: string;
  /**
   * 记录开始时间（精确到小时）
   */
  startTime?: string;
  /**
   * 更新时间戳（自动更新）
   */
  updateTime?: string;
};

/**
 *
 * @returns 获取流量统计列表
 */
export async function getFlowStatisticListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClient.get<FlowUsageRecordListDTO>(
    '/energyConsumptionManagement/flowUsageRecord/page',
    {
      params: {
        pageNumber: param.currentPage,
        pageSize: param.pageSize,
        ...search,
      },
    },
  );
}

/** 新增流量统计 */
export async function addFlowStatisticApi(data: FlowUsageRecordDTO) {
  return requestClient.post<FlowUsageRecordDTO>(
    '/energyConsumptionManagement/flowUsageRecord',
    data,
  );
}

/** 修改流量统计 */
export async function editFlowStatisticApi(data: FlowUsageRecordDTO) {
  return requestClient.put<FlowUsageRecordDTO>(
    '/energyConsumptionManagement/flowUsageRecord',
    data,
  );
}

/** 删除流量统计 */
export async function deleteFlowStatisticApi(recordId: string) {
  return requestClient.delete<string>(
    `/energyConsumptionManagement/flowUsageRecord/${recordId}`,
  );
}
