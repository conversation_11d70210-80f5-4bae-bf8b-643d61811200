<script setup lang="ts">
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type {
  CmWarningHistory,
  CmWarningRegister,
} from '#/api/core/accident-alarm/manage';
import type { CustomFormSchema } from '#/components/form-modal';

import { ref, shallowRef, useTemplateRef } from 'vue';

import { Button, message, Popconfirm } from 'ant-design-vue';
import { clone } from 'remeda';

import { updateAccidentAlarm } from '#/api/core/accident-alarm/manage';
import FormModal from '#/components/form-modal/index.vue';
import { PriorityMap } from '#/data-model/comm';

const emit = defineEmits<{
  (e: 'hadEdit'): void;
}>();

const manageDetailObj = shallowRef<CmWarningRegister>();
const commandDetailObj = shallowRef<CmWarningHistory>();
const pageType = ref<'add' | 'edit'>('add');

// 新建或编辑表单弹窗
const formModalRef =
  useTemplateRef<ComponentExposed<typeof FormModal>>('formModalRef');

const formOption: CustomFormSchema[] = [
  {
    fieldName: 'title',
    label: '事件标题',
    rules: 'required',
  },
  {
    fieldName: 'level',
    label: '优先级',
    rules: 'required',
    component: 'Select',
    componentProps: {
      options: Object.values(PriorityMap),
    },
  },
  {
    fieldName: 'overview',
    label: '事件概述',
    rules: 'required',
  },
  {
    fieldName: 'detail',
    label: '事件详情',
    rules: 'required',
    component: 'Textarea',
  },
];

/**
 * @description: 编辑
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  if (param.meta.type === 'edit') {
    // 在warningHistoryList找到当前正在编辑的对象
    if (!manageDetailObj.value?.warningHistoryList)
      return Promise.resolve(true);

    const index = manageDetailObj.value.warningHistoryList.findIndex(
      (item) => item.recordId === param.meta.raw.recordId,
    );
    if (index !== -1) {
      manageDetailObj.value.warningHistoryList[index] = {
        ...manageDetailObj.value?.warningHistoryList?.[index],
        ...param.formData,
      };
    }
    updateAccidentAlarm(manageDetailObj.value)
      .then(() => {
        message.success('修改指令成功');
        emit('hadEdit');
        return true;
      })
      .catch(() => false);
  } else if (param.meta.type === 'add') {
    param.formData.warningId = manageDetailObj.value?.warningId;
    manageDetailObj.value?.warningHistoryList?.push(param.formData);
    updateAccidentAlarm(manageDetailObj.value!)
      .then(() => {
        message.success('新增指令成功');
        emit('hadEdit');
        return true;
      })
      .catch(() => false);
  }

  return Promise.resolve(true);
}

/** 删除 */
function deleteSchedule() {
  // 在warningHistoryList找到当前正在编辑的对象
  if (!manageDetailObj.value?.warningHistoryList || !commandDetailObj.value)
    return Promise.resolve(true);

  const index = manageDetailObj.value.warningHistoryList.findIndex(
    (item) => item.recordId === commandDetailObj.value?.recordId,
  );
  if (index !== -1) {
    // 从数组中删除该项
    manageDetailObj.value.warningHistoryList.splice(index, 1);
  }

  updateAccidentAlarm(manageDetailObj.value!)
    .then(() => {
      message.success('删除指令成功');
      formModalRef.value?.modalApi.close();
      emit('hadEdit');
    })
    .catch(() => false);
}

function open(param: CmWarningRegister, param1?: CmWarningHistory) {
  manageDetailObj.value = param;
  commandDetailObj.value = param1;
  pageType.value = param1 ? 'edit' : 'add';

  formModalRef.value?.open({
    title: param1 ? '指令详情' : '新增指令',
    formData: param1,
    meta: { type: param1 ? 'edit' : 'add', raw: clone(param1) },
  });
}

defineExpose({
  open,
});
</script>

<template>
  <!-- 详情弹窗 -->
  <FormModal ref="formModalRef" :form-option="formOption" :submit="onSubmit">
    <template v-if="pageType === 'edit'" #center-footer>
      <Popconfirm title="是否确认删除?" @confirm="deleteSchedule()">
        <Button type="primary" danger>删除</Button>
      </Popconfirm>
    </template>
  </FormModal>
</template>
