<script setup lang="ts">
import type { StatisticItem } from '#/components/statistic-card.vue';

import { ref, shallowRef } from 'vue';
import { useRoute } from 'vue-router';

import { FallOutlined, RiseOutlined } from '@ant-design/icons-vue';
import {
  Progress,
  Table,
  TableSummary,
  TableSummaryCell,
  TableSummaryRow,
} from 'ant-design-vue';

import StatisticCard from '#/components/statistic-card.vue';

import { reportDataMap } from './data';

const route = useRoute();
const reportId = route.query.reportId as string;

const {
  factorPriceDetail = '{}',
  waterReportDetail = '[]',
  electricityReportDetail = '[]',
  ...data
} = { ...reportDataMap.value[reportId] };

const total = ref<StatisticItem[]>([
  {
    title: '总用水量',
    value: data.waterUsage,
    meta: { changePercentage: data.waterUsageRatio },
    color: '#1890ff',
    unit: 'm³',
    svgName: 'material-symbols:water-drop',
  },
  {
    title: '总用电量',
    value: data.electricityUsage,
    meta: { changePercentage: data.electricityUsageRatio },
    color: '#13ce66',
    unit: 'kWh',
    svgName: 'pepicons-pop:electricity-circle-filled',
  },
  {
    title: '用水成本',
    value: data.waterPrice,
    meta: { changePercentage: data.waterPriceRatio },
    color: '#1890ff',
    unit: '元',
    svgName: 'material-symbols:water',
  },
  {
    title: '用电成本',
    value: data.electricityPrice,
    meta: { changePercentage: data.electricityPriceRatio },
    color: '#13ce66',
    unit: '元',
    svgName: 'icon-park-outline:electric-wave',
  },
]);

// 用水计量详情数据
const waterUsageData = shallowRef(JSON.parse(waterReportDetail) ?? []);

// 用电计量详情数据
const electricityUsageData = shallowRef(
  JSON.parse(electricityReportDetail) ?? [],
);

// 表格列配置
const waterColumns = [
  {
    title: '区域',
    dataIndex: 'areaName',
    key: 'areaName',
  },
  {
    title: '用水量(吨)',
    dataIndex: 'usage',
    key: 'usage',
  },
  {
    title: '占比',
    dataIndex: 'usagePercentage',
    key: 'usagePercentage',
  },
  {
    title: '环比变化',
    dataIndex: 'usageRatio',
    key: 'usageRatio',
  },
];

const electricityColumns = [
  {
    title: '区域',
    dataIndex: 'areaName',
    key: 'areaName',
  },
  {
    title: '用电量(度)',
    dataIndex: 'usage',
    key: 'usage',
  },
  {
    title: '占比',
    dataIndex: 'usagePercentage',
    key: 'usagePercentage',
  },
  {
    title: '环比变化',
    dataIndex: 'usageRatio',
    key: 'usageRatio',
  },
];

const factorPriceColumns = [
  {
    title: '类别',
    dataIndex: 'category',
    key: 'category',
  },
  {
    title: '用量',
    dataIndex: 'usage',
    key: 'usage',
  },
  {
    title: '单价(元)',
    dataIndex: 'price',
    key: 'price',
  },
  {
    title: '总成本(元)',
    dataIndex: 'cost',
    key: 'cost',
  },
  {
    title: '环比变化',
    dataIndex: 'costRatio',
    key: 'costRatio',
  },
];

const factorPriceDetailData = JSON.parse(factorPriceDetail);
const factorPriceData = shallowRef([
  {
    category: '用水成本',
    usage: `${factorPriceDetailData.waterUsage} m³`,
    price: `¥ ${factorPriceDetailData.waterPrice}元/m³`,
    cost: `¥ ${factorPriceDetailData.waterTotalPrice}`,
    costRatio: factorPriceDetailData.waterPriceRatio,
  },
  {
    category: '用电成本',
    usage: `${factorPriceDetailData.electricityUsage} kWh`,
    price: `¥ ${factorPriceDetailData.electricityPrice}元/kWh`,
    cost: `¥ ${factorPriceDetailData.electricityTotalPrice}`,
    costRatio: factorPriceDetailData.electricityPriceRatio,
  },
]);

const totalPrice = factorPriceDetailData.totalPrice;
const totalPriceRatio = factorPriceDetailData.totalPriceRatio;
</script>
<template>
  <div class="p-4">
    <StatisticCard :total="total">
      <template #desc="{ item }">
        <p>
          <RiseOutlined
            v-if="item.meta.changePercentage >= 0"
            class="text-red-500"
          />
          <FallOutlined
            v-if="item.meta.changePercentage < 0"
            class="text-green-500"
          />
          <span
            :class="{
              'text-green-500': item.meta.changePercentage < 0,
              'text-red-500': item.meta.changePercentage >= 0,
            }"
          >
            {{ item.meta.changePercentage ?? 0 }}%
          </span>
          <span>较上月</span>
        </p>
      </template>
    </StatisticCard>

    <div class="rounded-lg bg-white">
      <div class="p-2 text-lg font-bold">用水计量详情</div>
      <Table
        :columns="waterColumns"
        :data-source="waterUsageData"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'usagePercentage'">
            <div class="flex items-center gap-2">
              <Progress
                class="m-0"
                :percent="record.usagePercentage"
                size="small"
                stroke-color="#1677ff"
                :show-info="false"
              />
              <div class="text-xs">{{ record.usagePercentage }}%</div>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'usageRatio'">
            <p>
              <RiseOutlined
                v-if="record.usageRatio >= 0"
                class="text-red-500"
              />
              <FallOutlined
                v-if="record.usageRatio < 0"
                class="text-green-500"
              />
              <span
                :class="{
                  'text-green-500': record.usageRatio < 0,
                  'text-red-500': record.usageRatio >= 0,
                }"
              >
                {{ record.usageRatio ?? 0 }}%
              </span>
            </p>
          </template>
          <template v-else>{{ record[column.key as string] }}</template>
        </template>
      </Table>
    </div>

    <div class="mt-4 rounded-lg bg-white">
      <div class="p-2 text-lg font-bold">用电计量详情</div>
      <Table
        :columns="electricityColumns"
        :data-source="electricityUsageData"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'usagePercentage'">
            <div class="flex items-center gap-2">
              <Progress
                class="m-0"
                :percent="record.usagePercentage"
                size="small"
                stroke-color="#1677ff"
                :show-info="false"
              />
              <div class="text-xs">{{ record.usagePercentage }}%</div>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'usageRatio'">
            <p>
              <RiseOutlined
                v-if="record.usageRatio >= 0"
                class="text-red-500"
              />
              <FallOutlined
                v-if="record.usageRatio < 0"
                class="text-green-500"
              />
              <span
                :class="{
                  'text-green-500': record.usageRatio < 0,
                  'text-red-500': record.usageRatio >= 0,
                }"
              >
                {{ record.usageRatio ?? 0 }}%
              </span>
            </p>
          </template>
          <template v-else>{{ record[column.key as string] }}</template>
        </template>
      </Table>
    </div>

    <div class="mt-4 rounded-lg bg-white">
      <div class="p-2 text-lg font-bold">用能成本详情</div>
      <Table
        :columns="factorPriceColumns"
        :data-source="factorPriceData"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'costRatio'">
            <p>
              <RiseOutlined v-if="record.costRatio >= 0" class="text-red-500" />
              <FallOutlined
                v-if="record.costRatio < 0"
                class="text-green-500"
              />
              <span
                :class="{
                  'text-green-500': record.costRatio < 0,
                  'text-red-500': record.costRatio >= 0,
                }"
              >
                {{ record.costRatio ?? 0 }}%
              </span>
            </p>
          </template>
          <template v-else>{{ record[column.key as string] }}</template>
        </template>
        <template #summary>
          <TableSummary fixed>
            <TableSummaryRow>
              <TableSummaryCell :index="0">
                <div class="font-bold">总计</div>
              </TableSummaryCell>
              <TableSummaryCell :index="1" />
              <TableSummaryCell :index="2" />
              <TableSummaryCell :index="3">
                <div class="font-bold">{{ `¥ ${totalPrice}` }}</div>
              </TableSummaryCell>
              <TableSummaryCell :index="4">
                <p>
                  <RiseOutlined
                    v-if="totalPriceRatio >= 0"
                    class="text-red-500"
                  />
                  <FallOutlined
                    v-if="totalPriceRatio < 0"
                    class="text-green-500"
                  />
                  <span
                    :class="{
                      'text-green-500': totalPriceRatio < 0,
                      'text-red-500': totalPriceRatio >= 0,
                    }"
                  >
                    {{ totalPriceRatio ?? 0 }}%
                  </span>
                </p>
              </TableSummaryCell>
            </TableSummaryRow>
          </TableSummary>
        </template>
      </Table>
    </div>
  </div>
</template>
