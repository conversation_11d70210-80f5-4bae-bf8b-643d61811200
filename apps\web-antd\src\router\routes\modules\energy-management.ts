import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:energy',
      title: '能源管理',
    },
    name: 'EnergyManagement',
    path: '/energy-management',
    children: [
      {
        meta: {
          icon: 'mdi:database',
          title: '基础数据',
        },
        name: 'EnergyManagementBaseData',
        path: '/energy-management/base-data',
        children: [
          {
            meta: {
              title: '能源介质类型',
            },
            name: 'EnergyManagementEnergyMediumType',
            path: '/energy-management/energy-medium-type',
            component: () =>
              import('#/views/energy-management/energy-medium-type/index.vue'),
          },
          {
            meta: {
              title: '时段类型',
            },
            name: 'EnergyManagementPeriodType',
            path: '/energy-management/period-type',
            component: () =>
              import('#/views/energy-management/period-type/index.vue'),
          },
          {
            meta: {
              title: '时段配置',
            },
            name: 'EnergyManagementPeriodConfig',
            path: '/energy-management/period-config',
            component: () =>
              import('#/views/energy-management/period-config/index.vue'),
          },
          {
            meta: {
              title: '能源单价',
            },
            name: 'EnergyManagementEnergyPrice',
            path: '/energy-management/energy-price',
            component: () =>
              import('#/views/energy-management/energy-price/index.vue'),
          },
          {
            meta: {
              title: '标煤折算系数',
            },
            name: 'EnergyManagementCoalConversionFactor',
            path: '/energy-management/coal-conversion-factor',
            component: () =>
              import(
                '#/views/energy-management/coal-conversion-factor/index.vue'
              ),
            props: true,
          },
        ],
      },
      {
        meta: {
          title: '能耗管理',
          icon: 'lsicon:consume-filled',
        },
        name: 'EnergyManagementEnergyManage',
        path: '/energy-management/consume-manage',
        redirect: '/energy-management/area-manage',
        children: [
          {
            meta: {
              title: '区域管理',
            },
            name: 'EnergyManagementAreaManage',
            path: '/energy-management/area-manage',
            component: () =>
              import('#/views/energy-management/area-manage/index.vue'),
          },
          {
            meta: {
              title: '设备管理',
            },
            name: 'EnergyManagementDeviceManage',
            path: '/energy-management/device-manage',
            component: () =>
              import('#/views/energy-management/device-manage/index.vue'),
          },
          {
            meta: {
              title: '用水计量与统计',
            },
            name: 'EnergyManagementWaterStatistic',
            path: '/energy-management/water-statistic',
            component: () =>
              import('#/views/energy-management/water-statistic/index.vue'),
          },
          {
            meta: {
              title: '用电计量与统计',
            },
            name: 'EnergyManagementElectricStatistic',
            path: '/energy-management/electric-statistic',
            component: () =>
              import('#/views/energy-management/electric-statistic/index.vue'),
          },
          {
            meta: {
              title: '流量计量与统计',
            },
            name: 'EnergyManagementFlowStatistic',
            path: '/energy-management/flow-statistic',
            component: () =>
              import('#/views/energy-management/flow-statistic/index.vue'),
          },
          {
            meta: {
              title: '能耗统计',
            },
            name: 'EnergyManagementConsumeStatistic',
            path: '/energy-management/consume-statistic',
            component: () =>
              import('#/views/energy-management/consume-statistic/index.vue'),
          },
        ],
      },
      {
        meta: {
          icon: 'material-symbols:monitoring',
          title: '能耗监测',
        },
        name: 'EnergyMonitor',
        path: '/energy-monitor',
        children: [
          {
            meta: {
              title: '能耗/标煤对比分析',
            },
            name: 'EnergyMonitorEnergyCoalComparisonAnalysis',
            path: '/energy-management/energy-coal-comparison-analysis',
            component: () =>
              import(
                '#/views/energy-management/energy-coal-comparison-analysis/index.vue'
              ),
          },
          {
            meta: {
              title: '能源分类分项',
            },
            name: 'EnergyMonitorEnergyClassification',
            path: '/energy-management/energy-classification',
            component: () =>
              import(
                '#/views/energy-management/energy-classification/index.vue'
              ),
          },
          {
            meta: {
              title: '能流图',
            },
            name: 'EnergyMonitorEnergyFlowChart',
            path: '/energy-management/energy-flow-chart',
            component: () =>
              import('#/views/energy-management/energy-flow-chart/index.vue'),
          },
          {
            meta: {
              title: '能耗报表',
            },
            name: 'EnergyMonitorEnergyConsumptionReport',
            path: '/energy-management/energy-consumption-report',
            component: () =>
              import(
                '#/views/energy-management/energy-consumption-report/index.vue'
              ),
          },
          {
            meta: {
              title: '能耗报表详情',
              hideInMenu: true,
            },
            name: 'EnergyMonitorEnergyConsumptionReportDetail',
            path: '/energy-management/energy-consumption-report-detail',
            component: () =>
              import(
                '#/views/energy-management/energy-consumption-report/detail.vue'
              ),
          },
          {
            meta: {
              title: '告警配置',
            },
            name: 'EnergyMonitorAlarmConfig',
            path: '/energy-management/alarm-config',
            component: () =>
              import('#/views/energy-management/alarm-config/index.vue'),
          },
          {
            meta: {
              title: '异常提醒',
            },
            name: 'EnergyMonitorAlarmRecord',
            path: '/energy-management/alarm-record',
            component: () =>
              import('#/views/energy-management/alarm-record/index.vue'),
          },
        ],
      },
      {
        meta: {
          title: '碳排放管理',
          icon: 'material-symbols:nest-eco-leaf-outline',
        },
        name: 'EnergyManagementCarbonEmission',
        path: '/energy-management/carbon-emission',
        children: [
          {
            meta: {
              title: '碳排放计划',
            },
            name: 'EnergyManagementCarbonEmissionPlan',
            path: '/energy-management/carbon-emission-plan',
            component: () =>
              import(
                '#/views/energy-management/carbon-emission-plan/index.vue'
              ),
            props: true,
          },
          {
            meta: {
              title: '碳排放模板',
            },
            name: 'EnergyManagementCarbonEmissionCalculate',
            path: '/energy-management/carbon-emission-calculate',
            component: () =>
              import(
                '#/views/energy-management/carbon-emission-calculate/index.vue'
              ),
            props: true,
          },
          {
            meta: {
              title: '告警配置',
            },
            name: 'EnergyManagementCarbonEmissionAlarmConfig',
            path: '/energy-management/carbon-emission-alarm-config',
            component: () =>
              import(
                '#/views/energy-management/carbon-emission-alarm-config/index.vue'
              ),
          },
          {
            meta: {
              title: '异常提醒',
            },
            name: 'EnergyManagementCarbonEmissionAlarmRecord',
            path: '/energy-management/carbon-emission-alarm-record',
            component: () =>
              import(
                '#/views/energy-management/carbon-emission-alarm-record/index.vue'
              ),
          },
          {
            meta: {
              title: '统计分析',
            },
            name: 'EnergyManagementCarbonEmissionStatistic',
            path: '/energy-management/carbon-emission-statistic',
            component: () =>
              import(
                '#/views/energy-management/carbon-emission-statistic/index.vue'
              ),
          },
          {
            meta: {
              title: '统计分析-已发布',
              query: {
                type: 'show',
              },
            },
            name: 'EnergyManagementCarbonEmissionStatisticPublished',
            path: '/energy-management/carbon-emission-statistic-published',
            component: () =>
              import(
                '#/views/energy-management/carbon-emission-statistic/index.vue'
              ),
          },
          {
            meta: {
              title: '知识库',
            },
            name: 'EnergyManagementCarbonEmissionKnowledgeBase',
            path: '/energy-management/carbon-emission-knowledge-base',
            component: () =>
              import(
                '#/views/energy-management/carbon-emission-knowledge-base/index.vue'
              ),
          },
          {
            meta: {
              title: '知识库文件',
              hideInMenu: true,
              activePath: '/energy-management/carbon-emission-knowledge-base',
            },
            name: 'EnergyManagementCarbonEmissionKnowledgeBaseFile',
            path: '/energy-management/carbon-emission-knowledge-base-file/:knowledgeId',
            component: () =>
              import(
                '#/views/energy-management/carbon-emission-knowledge-base/knowledge-base-file/index.vue'
              ),
            props: true,
          },
        ],
      },
    ],
  },
];

export default routes;
