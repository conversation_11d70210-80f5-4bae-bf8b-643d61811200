/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-28
 * @LastEditors: <PERSON>rayer
 * @LastEditTime: 2025-07-29
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\accident-alarm\manage\data.ts
 */
import type { Component } from 'vue';

/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-28
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\accident-alarm\manage\data.ts
 */
import type { CmWarningRegister } from '#/api/core/accident-alarm/manage';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import {
  SvgExclamatoryCircleIcon,
  SvgExclamatoryTriangleIcon,
  SvgICircleIcon,
} from '@vben/icons';

import { StatusMap } from '#/data-model/comm';

// 预警等级map
export const LevelMap: Record<
  string,
  {
    color: string;
    component?: Component;
    label: string;
    tag?: string;
    value: string;
  }
> = {
  重大预警: {
    value: '重大预警',
    label: '重大预警',
    color: 'red',
    component: SvgExclamatoryCircleIcon,
    tag: '紧急',
  },
  中等预警: {
    value: '中等预警',
    label: '中等预警',
    color: 'orange',
    component: SvgExclamatoryTriangleIcon,
    tag: '需关注',
  },
  一般预警: {
    value: '一般预警',
    label: '一般预警',
    color: 'blue',
    component: SvgICircleIcon,
    tag: '留意',
  },
};

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '事件名称',
    },
    {
      fieldName: 'level',
      label: '事件等级',
      component: 'Select',
      componentProps: {
        options: Object.values(LevelMap),
      },
    },
    {
      fieldName: 'status',
      label: '处理状态',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },
    {
      fieldName: 'type',
      label: '事件类型',
    },
    {
      fieldName: 'dateRange',
      label: '日期范围',
      component: 'RangePicker',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CmWarningRegister> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'warningTitle',
      title: '事件标题',
    },
    {
      field: 'warningType',
      title: '事件类型',
    },
    {
      field: 'warningLevel',
      title: '事件等级',
      formatter: (param: any) => {
        const cellValue = param.row.warningLevel;
        return LevelMap[cellValue]?.label ?? cellValue ?? '';
      },
    },
    {
      field: 'occurrenceTime',
      title: '发生时间',
      customType: 'date',
    },
    // 处理状态
    {
      field: 'processingStatus',
      title: '处理状态',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.processingStatus;
        return [StatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'warningTitle',
      label: '事件标题',
      rules: 'required',
    },
    {
      fieldName: 'warningType',
      label: '事件类型',
      rules: 'required',
    },
    {
      fieldName: 'warningLevel',
      label: '事件等级',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(LevelMap),
      },
    },
    {
      fieldName: 'occurrenceTime',
      label: '发生时间',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        showTime: true,
      },
    },
    {
      fieldName: 'reporterName',
      label: '事件上报人',
      rules: 'required',
    },
    {
      fieldName: 'responsibleDepartment',
      label: '负责部门',
      rules: 'required',
    },
    {
      fieldName: 'doverview',
      label: '概述',
      rules: 'required',
      component: 'Textarea',
    },
  ];
}
