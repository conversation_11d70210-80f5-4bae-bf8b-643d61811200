<script lang="ts" setup>
import type { ShallowRef } from 'vue';
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { EmergencyEquipment } from '#/api/core/emergency/equipment';
import type { StorePoint } from '#/api/core/emergency/store-point';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addEmergencyEquipmentApi,
  deleteEmergencyEquipmentApi,
  editEmergencyEquipmentApi,
  getEmergencyEquipmentDetailApi,
  getEmergencyEquipmentListApi,
} from '#/api/core/emergency/equipment';
import { getStorePointListApi } from '#/api/core/emergency/store-point';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';
import Detail from './detail.vue';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');
const detailRef = useTemplateRef<ComponentExposed<typeof Detail>>('detailRef');

const hadLoad = ref(false);
const topSearchScheme: ShallowRef<CustomFormSchema[]> = shallowRef([]);
const columnsScheme: ShallowRef<CustomColumnSchema<EmergencyEquipment>> =
  shallowRef([]);
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef([]);

const storePointList = shallowRef<StorePoint[]>([]);

// 获取应急存放点列表
getStorePointListApi({ pageSize: 99_999, currentPage: 1 }, {}).then((res) => {
  topSearchScheme.value = getTopSearchScheme(res.items);
  columnsScheme.value = getColumnsScheme();
  formOption.value = getFormOption(res.items);
  storePointList.value = res.items;
  hadLoad.value = true;
});

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<EmergencyEquipment>,
  fromData: any,
) {
  if (fromData.timeRange) {
    fromData.beginDate = fromData.timeRange[0].startOf('day').toISOString();
    fromData.endDate = fromData.timeRange[1].endOf('day').toISOString();
    delete fromData.timeRange;
  }

  return getEmergencyEquipmentListApi(param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: EmergencyEquipment) {
  return deleteEmergencyEquipmentApi(row.equipmentId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: EmergencyEquipment) {
  gridFormRef.value?.gridApi.setLoading(true);
  getEmergencyEquipmentDetailApi(row.equipmentId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '编辑应急装备',
        formData: {
          ...res,
          cmEquipmentImageList: res.cmEquipmentImageList?.map((item) => ({
            uid: item.imageId,
            name:
              item.imageName ??
              item.imagePath?.slice(
                Math.max(0, item.imagePath.lastIndexOf('/') + 1),
              ) ??
              'file',
            status: 'done',
            url: item.imagePath,
          })),
          cmInventoryLocationId: res.cmInventory?.locationId,
          cmInventoryInfoCurrentQuantity: res.cmInventory?.quantity,
          cmInventoryInfoSafetyQuantity: res.cmInventory?.safetyStock,
          cmInventoryStorageDate: res.cmInventory?.storageDate,
          cmInventoryPurchaseDate: res.cmInventory?.purchaseDate,
          cmInventoryShelfLife: res.cmInventory?.shelfLife,
          cmInventoryPurchasePrice: res.cmInventory?.purchasePrice,
          cmInventorySupplier: res.cmInventory?.supplier,
        },
        meta: { type: 'edit', raw: row },
      });
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: EmergencyEquipment;
}): Promise<boolean> {
  if (param.formData.cmEquipmentImageList) {
    param.formData.cmEquipmentImageList =
      param.formData.cmEquipmentImageList.map((item: any) => ({
        equipmentId: param.raw.equipmentId,
        imagePath: item.url,
      }));
  }
  param.formData.cmInventory = {
    equipmentId: param.raw.equipmentId,
    locationId: param.formData.cmInventoryLocationId,
    quantity: param.formData.cmInventoryInfoCurrentQuantity,
    safetyStock: param.formData.cmInventoryInfoSafetyQuantity,
    storageDate: param.formData.cmInventoryStorageDate,
    purchaseDate: param.formData.cmInventoryPurchaseDate,
    shelfLife: param.formData.cmInventoryShelfLife,
    purchasePrice: param.formData.cmInventoryPurchasePrice,
    supplier: param.formData.cmInventorySupplier,
  };

  if (param.meta.type === 'create')
    addEmergencyEquipmentApi(param.formData)
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editEmergencyEquipmentApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="应急装备列表"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增应急装备',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="detailRef?.open(row, storePointList)">
            查看
          </Button>
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
    <Detail ref="detailRef" @close="gridFormRef?.gridApi.query()" />
  </Page>
</template>
