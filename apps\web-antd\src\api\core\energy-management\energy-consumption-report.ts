import type { Dayjs } from 'dayjs';

import { requestClient } from '#/api/request';

// 能耗报表类型
export interface EnMeasurementReport {
  /** 报表唯一标识符 */
  reportId?: string;
  /** 报表编码 */
  reportCode: string;
  /** 记录日期 */
  reportDate: string;
  /** 总用水量（单位:m³） */
  waterUsage: number;
  /** 总用电量（单位:kWh） */
  electricityUsage: number;
  /** 用水成本（单位:元） */
  waterPrice: number;
  /** 用电成本（单位:元） */
  electricityPrice: number;
  /** 用水计量详情 */
  waterReportDetail?: string;
  /** 用电计量详情 */
  electricityReportDetail?: string;
  /** 用能成本详情 */
  factorPriceDetail?: string;
}

// 分页查询
export async function getMeasurementReportListApi(
  page: { currentPage: number; pageSize: number },
  params: { date: Dayjs },
) {
  if (!params.date) return;
  return requestClient.get<any>(
    '/energyConsumptionMonitoring/measurementReport/page',
    {
      params: {
        date: params.date.startOf('day').toISOString(),
        pageNumber: page.currentPage,
        pageSize: page.pageSize,
      },
    },
  );
}

// 新增
export async function addMeasurementReportApi(data: EnMeasurementReport) {
  return requestClient.post<any>(
    '/energyConsumptionMonitoring/measurementReport',
    data,
  );
}

// 编辑
export async function editMeasurementReportApi(data: EnMeasurementReport) {
  return requestClient.put<any>(
    '/energyConsumptionMonitoring/measurementReport',
    data,
  );
}

// 删除
export async function deleteMeasurementReportApi(reportId: string) {
  return requestClient.delete<any>(
    `/energyConsumptionMonitoring/measurementReport/${reportId}`,
  );
}

// 新增前获取用能计量数据
export async function getMeasurementReportDefaultApi(params: {
  date: string;
  type: 1 | 2;
}) {
  return requestClient.get<any>(
    '/energyConsumptionMonitoring/measurementReportDefault',
    {
      params,
    },
  );
}
