import type { CmDrillBasicInfo } from '#/api/core/emergency-drill/drill-evaluate';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-01
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency-drill\drill-evaluate\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { getDrillEvaluateListApi } from '#/api/core/emergency-drill/drill-evaluate';

// 状态
export const StatusMap: Record<
  string,
  { color: string; label: string; value: string }
> = {
  优: {
    value: '优',
    label: '优',
    color: 'gold',
  },
  良: {
    value: '良',
    label: '良',
    color: 'green',
  },
  中: {
    value: '中',
    label: '中',
    color: 'blue',
  },
  差: {
    value: '差',
    label: '差',
    color: 'red',
  },
};

// 评估项目
export type EvaluateProject = {
  idea: string;
  name: string;
  score: number;
  target: string;
  weight?: number;
};

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '演练标题',
    },
    {
      fieldName: 'type',
      label: '演练类型',
    },
    {
      fieldName: 'status',
      label: '演练结果',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },
    {
      fieldName: 'timeRange',
      label: '时间范围',
      component: 'RangePicker',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CmDrillBasicInfo> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'title',
      title: '演练标题',
      customType: 'userInfo',
      custom: {
        userInfo: {
          name: (row: CmDrillBasicInfo) => row.title ?? '',
          desc: (row: CmDrillBasicInfo) => `编号：${row.drillId}`,
        },
      },
    },
    // 演练类型
    {
      field: 'type',
      title: '演练类型',
    },
    // 组织单位
    {
      field: 'organizer',
      title: '组织单位',
    },
    // 演练时间
    {
      field: 'drillTime',
      title: '演练时间',
      customType: 'date',
    },
    // 演练结果
    {
      field: 'evaluationResult',
      title: '演练结果',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.evaluationResult;
        return [StatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    // 评估分数
    {
      field: 'fraction',
      title: '评估分数',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'title',
      label: '演练标题',
      disabled: true,
    },
    // 评估日期
    {
      fieldName: 'valuationTime',
      label: '评估日期',
      rules: 'required',
      component: 'DatePicker',
    },
    // 评估负责人
    {
      fieldName: 'person',
      label: '评估负责人',
      rules: 'required',
    },
    // 评估部门
    {
      fieldName: 'department',
      label: '评估部门',
      rules: 'required',
    },
    // 评估结果
    {
      fieldName: 'evaluationResult',
      label: '评估结果',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },

    {
      component: 'Divider',
      fieldName: '',
      label: '评估项目评分',
    },

    // 评估项目评分
    {
      fieldName: 'rating',
      label: '评估项目评分',
      component: 'Add',
      custom: {
        addFormConfig: {
          wrapperClass: 'grid-cols-2',
        },
        addObj: [
          {
            fieldName: 'name',
            label: '评估项目',
            rules: 'required',
          },
          {
            fieldName: 'target',
            label: '评估指标',
            rules: 'required',
          },
          // 权重
          {
            fieldName: 'weight',
            label: '权重(%)',
            rules: 'required',
            component: 'InputNumber',
          },
          // 得分
          {
            fieldName: 'score',
            label: '得分(0-100)',
            rules: 'required',
            component: 'InputNumber',
          },
          // 评估意见
          {
            fieldName: 'idea',
            label: '评估意见',
            component: 'Textarea',
          },
        ],
      },
    },

    {
      component: 'Divider',
      fieldName: '',
      label: '总体评价',
    },

    // 评估总结
    {
      fieldName: 'opinion',
      label: '评估总结',
      component: 'Textarea',
    },
    // 优点与成效
    {
      fieldName: 'advantage',
      label: '优点与成效',
      component: 'Textarea',
    },
    // 不足
    {
      fieldName: 'insufficient',
      label: '问题与不足',
      component: 'Textarea',
    },
    // 改进建议
    {
      fieldName: 'suggestion',
      label: '改进建议',
      component: 'Textarea',
    },
  ];
}

// 选择关联的演练记录
export function getFormOptionRecord(): CustomFormSchema[] {
  return [
    {
      fieldName: 'drillId',
      label: '演练记录',
      rules: 'required',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getDrillEvaluateListApi(
            true,
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              title: keyword,
            },
          ).then((res) => res.items.filter((item) => !item.evaluationResult));

          return tableDataAll.map((item) => ({
            label: item.title ?? '',
            value: item.drillId,
          }));
        },
      },
    },
  ];
}
