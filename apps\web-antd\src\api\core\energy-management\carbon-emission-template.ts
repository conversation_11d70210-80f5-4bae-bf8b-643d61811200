import type { EnDevice } from './device-manage';

import { requestClient } from '#/api/request';

// 碳排放模板接口类型
export interface EnCarbonEmissionTemplate {
  templateId?: number;
  templateName: string;
  templateData: string;
  status: number;
  createdTime?: string;
  updatedTime?: string;
}

// 分页查询参数
export interface CarbonEmissionTemplatePageParams {
  templateName?: string;
  pageNumber: number;
  pageSize: number;
}

// 分页响应结果
export interface CarbonEmissionTemplatePageResult {
  total: number;
  data: EnCarbonEmissionTemplate[];
}

// 通用响应结果
export interface CommonResult<T = any> {
  code: number;
  message: string;
  data: T;
}

/**
 * 分页查询碳排放模板
 */
export async function getCarbonEmissionTemplatePageApi(
  params: CarbonEmissionTemplatePageParams,
): Promise<CarbonEmissionTemplatePageResult> {
  return requestClient.get('/carbon/template/page', {
    params,
  });
}

/**
 * 新增碳排放模板
 */
export async function addCarbonEmissionTemplateApi(
  data: Omit<
    EnCarbonEmissionTemplate,
    'createdTime' | 'templateId' | 'updatedTime'
  >,
): Promise<CommonResult<EnCarbonEmissionTemplate>> {
  return requestClient.post('/carbon/template', data);
}

/**
 * 修改碳排放模板
 */
export async function editCarbonEmissionTemplateApi(
  data: EnCarbonEmissionTemplate,
): Promise<CommonResult<EnCarbonEmissionTemplate>> {
  return requestClient.put('/carbon/template', data);
}

/**
 * 删除碳排放模板
 */
export async function deleteCarbonEmissionTemplateApi(
  templateId: number,
): Promise<CommonResult<string>> {
  return requestClient.delete(`/carbon/template/${templateId}`);
}

/**
 * 获取所有设备列表
 */
export async function getDeviceListApi(): Promise<EnDevice[]> {
  return requestClient.get('/energyConsumptionManagement/device/list');
}
