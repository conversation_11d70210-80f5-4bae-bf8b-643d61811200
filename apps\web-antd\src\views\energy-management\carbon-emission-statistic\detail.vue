<!--
 * @Author: Strayer
 * @Date: 2025-08-19
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-19
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\energy-management\carbon-emission-statistic\detail.vue
-->
<script setup lang="ts">
import type { ComponentExposed } from 'vue-component-type-helpers';

import type {
  CalcOrderDTO,
  CarbonEmissionStatistic,
} from '#/api/core/energy-management/carbon-emission-statistic';
import type { CustomColumnSchema } from '#/components/grid-form';

import { shallowRef, useTemplateRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { getCarbonEmissionStatisticDetailApi } from '#/api/core/energy-management/carbon-emission-statistic';
import GridForm from '#/components/grid-form/index.vue';

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const equipment = shallowRef<CarbonEmissionStatistic>();

const [Modal, modalApi] = useVbenModal({
  footer: false,
  onCancel() {
    modalApi.close();
    emit('close');
  },
});

function open(param: CarbonEmissionStatistic) {
  equipment.value = param;
  modalApi.open();
}

defineExpose({
  open,
});

// 检测报告
const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const columnsScheme: CustomColumnSchema<CalcOrderDTO> = [
  { field: 'deviceId', title: 'ID', visible: false },
  { field: 'deviceName', title: '设备名称' },
  {
    field: 'metrics',
    title: '维度',
    formatter: ({ cellValue }: { cellValue: number }) => {
      return cellValue === 1 ? '用水' : '用电';
    },
  },
  {
    field: 'nodeData',
    title: '节点指标',
    formatter: ({ row }: { row: CalcOrderDTO }) => {
      return row.nodeData + (row.metrics === 1 ? 'm³' : 'kWh');
    },
  },
  {
    field: 'value',
    title: '设备用量',
    formatter: ({ row }: { row: CalcOrderDTO }) => {
      return row.value + (row.metrics === 1 ? 'm³' : 'kWh');
    },
  },
];

/**
 * @description: 获取表格数据
 */
async function query() {
  return getCarbonEmissionStatisticDetailApi(equipment.value?.id ?? 0).then(
    (res) => ({
      items: res,
      total: res.length,
    }),
  );
}
</script>

<template>
  <Modal class="w-[800px]" title="详情">
    <GridForm
      :grid-options="{
        exportConfig: {
          types: ['xlsx'],
        },
        toolbarConfig: {
          custom: true,
          export: true,
          refresh: { code: 'query' },
          zoom: true,
        },
      }"
      :disable-page="true"
      ref="gridFormRef"
      :columns-scheme="columnsScheme"
      :query="query"
    />
  </Modal>
</template>
