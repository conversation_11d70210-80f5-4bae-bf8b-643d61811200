<!--
 * @Author: Strayer
 * @Date: 2025-07-23
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-23
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\components\QRcode\core.vue
-->
<script lang="ts" setup>
import { shallowRef, useTemplateRef } from 'vue';
import vueQr from 'vue-qr/src/packages/vue-qr.vue';

import { saveAs } from 'file-saver';

export type QRcodeType = {
  text?: string;
};

const props = defineProps<{
  downloadName?: string;
  qrContent: QRcodeType;
}>();

const qrRef = useTemplateRef<HTMLElement>('qrRef');

const qrBlob = shallowRef<Blob>();

// 回调函数获取二维码 Base64
function getCodeUrl(dataURL: string) {
  // 移除 Base64 前缀
  const base64Data = dataURL.replace(/^data:image\/\w+;base64,/, '');
  // 转换为 Uint8Array
  // eslint-disable-next-line unicorn/prefer-code-point
  const byteArray = Uint8Array.from(atob(base64Data), (c) => c.charCodeAt(0));
  // 生成 Blob 对象
  qrBlob.value = new Blob([byteArray], { type: 'image/png' });
}

/** 下载二维码 */
function downloadQr() {
  saveAs(qrBlob.value ?? '', `${props.downloadName ?? '二维码'}.png`); // 调用 file-saver
}

defineExpose({
  downloadQr,
});
</script>
<template>
  <vueQr ref="qrRef" :text="props.qrContent.text" :callback="getCodeUrl" />
</template>
