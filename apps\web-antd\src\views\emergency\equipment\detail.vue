<script setup lang="ts">
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { EmergencyEquipment } from '#/api/core/emergency/equipment';
import type { StorePoint } from '#/api/core/emergency/store-point';

import { shallowRef, useTemplateRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Card, Image, ImagePreviewGroup } from 'ant-design-vue';
import dayjs from 'dayjs';

import { EmergencyMaterialStatusMap } from '#/api/core/emergency/material';
import QRcode from '#/components/QRcode/index.vue';

const QRcodeRef = useTemplateRef<ComponentExposed<typeof QRcode>>('QRcodeRef');

const equipmentObj = shallowRef<EmergencyEquipment>();

const storePointList = shallowRef<StorePoint[]>([]);

const [Modal, modalApi] = useVbenModal({
  footer: false,
  onCancel() {
    modalApi.close();
  },
});

function open(param: EmergencyEquipment, storePoint: StorePoint[]) {
  equipmentObj.value = param;
  storePointList.value = storePoint;
  modalApi.open();
}

defineExpose({
  open,
});
</script>

<template>
  <Modal class="w-[880px]" title="装备详情">
    <!-- 设备基本信息展示 -->
    <div v-if="equipmentObj">
      <Card title="基本信息" size="small">
        <Button
          class="absolute right-3 top-0.5"
          type="primary"
          @click="
            QRcodeRef?.open({
              qrContent: {
                text: `装备编码：${equipmentObj.equipmentId ?? ''}`,
              },
            })
          "
        >
          生成二维码
        </Button>
        <div class="grid grid-cols-2 gap-2">
          <div>
            <label class="mr-3">装备编码：</label>
            {{ equipmentObj.equipmentId }}
          </div>
          <div>
            <label class="mr-3">装备名称：</label>
            {{ equipmentObj.name }}
          </div>
          <div>
            <label class="mr-3">装备类型：</label>
            {{ equipmentObj.typeId }}
          </div>
          <div>
            <label class="mr-3">所属单位：</label>
            {{ equipmentObj.unitId }}
          </div>
          <div>
            <label class="mr-3">规格型号：</label>
            {{ equipmentObj.specification }}
          </div>
          <div>
            <label class="mr-3">品牌：</label>
            {{ equipmentObj.brand }}
          </div>
          <div>
            <label class="mr-3">生产厂家：</label>
            {{ equipmentObj.manufacturer }}
          </div>
          <div>
            <label class="mr-3">责任人：</label>
            {{ equipmentObj.responsibleId }}
          </div>
          <div>
            <label class="mr-3">存放位置：</label>
            {{
              storePointList.find(
                (item) =>
                  item.locationId === equipmentObj?.cmInventory?.locationId,
              )?.locationName ?? ''
            }}
          </div>
          <div>
            <label class="mr-3">库存数量：</label>
            {{ equipmentObj?.cmInventory?.quantity }} ({{ equipmentObj.unit }})
          </div>
          <div>
            <label class="mr-3">安全库存：</label>
            {{ equipmentObj?.cmInventory?.safetyStock }} ({{
              equipmentObj.unit
            }})
          </div>
          <div>
            <label class="mr-3">入库日期：</label>
            {{
              dayjs(equipmentObj?.cmInventory?.storageDate).format('YYYY-MM-DD')
            }}
          </div>
          <div>
            <label class="mr-3">购买日期：</label>
            {{
              dayjs(equipmentObj?.cmInventory?.purchaseDate).format(
                'YYYY-MM-DD',
              )
            }}
          </div>
          <div>
            <label class="mr-3">保质期(月)：</label>
            {{ equipmentObj?.cmInventory?.shelfLife }}
          </div>
          <div>
            <label class="mr-3">状态：</label>
            {{
              EmergencyMaterialStatusMap[equipmentObj.status ?? ''] ??
              equipmentObj.status ??
              ''
            }}
          </div>
          <div>
            <label class="mr-3">采购价格：</label>
            {{ equipmentObj?.cmInventory?.purchasePrice }}
          </div>
          <div>
            <label class="mr-3">供应商：</label>
            {{ equipmentObj?.cmInventory?.supplier }}
          </div>
        </div>
        <div class="pt-5">
          <div class="mr-3 font-bold">备注：</div>
          <div class="m-2">
            {{ equipmentObj.remarks }}
          </div>
        </div>
      </Card>
      <Card title="图片" class="mt-4" size="small">
        <ImagePreviewGroup>
          <div class="flex flex-wrap gap-2 overflow-hidden">
            <div
              class="h-[200px] w-[200px] overflow-hidden rounded-sm border border-gray-300"
              v-for="item in equipmentObj.cmEquipmentImageList"
              :key="item.imageId ?? item.imagePath"
            >
              <Image
                :width="200"
                :height="200"
                :src="item.imagePath"
                class="h-full w-full object-cover"
              />
            </div>
          </div>
        </ImagePreviewGroup>
      </Card>
    </div>
    <QRcode ref="QRcodeRef" title="装备二维码" />
  </Modal>
</template>
<style lang="css" scoped>
label {
  color: oklch(44.6% 0.043 257.281deg);
}
</style>
