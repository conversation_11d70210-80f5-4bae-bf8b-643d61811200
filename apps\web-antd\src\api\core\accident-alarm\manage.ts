/*
 * @Author: Strayer
 * @Date: 2025-07-28
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-29
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\api\core\accident-alarm\manage.ts
 */

import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * CmWarningRegister对象
 *
 * CmWarningRegister
 */
export type CmWarningRegister = {
  /**
   * 概述
   */
  doverview?: string;
  /**
   * 预警发生的时间
   */
  occurrenceTime?: string;
  /**
   * 预警的处理状态（默认未处理）
   */
  processingStatus?: string;
  /**
   * 事件上报人姓名
   */
  reporterName?: string;
  /**
   * 负责处理预警的部门
   */
  responsibleDepartment?: string;
  /**
   * 历史指令
   */
  warningHistoryList?: CmWarningHistory[];
  /**
   * 预警的唯一标识符（主键）
   */
  warningId: string;
  /**
   * 预警等级（重大/中等/一般预警）
   */
  warningLevel?: string;
  /**
   * 预警的标题信息
   */
  warningTitle?: string;
  /**
   * 预警类型（化工安全/建筑安全等）
   */
  warningType?: string;
};

/**
 * CmWarningHistory对象
 *
 * CmWarningHistory
 */
export type CmWarningHistory = {
  /**
   * 记录创建时间（默认当前时间）
   */
  createTime?: string;
  /**
   * 明细
   */
  detail?: string;
  /**
   * 等级
   */
  level?: string;
  overview?: string;
  /**
   * 预警处理的结果描述
   */
  processingResult?: string;
  /**
   * 预警处理的时间
   */
  processingTime?: string;
  /**
   * 预警历史记录的唯一标识符（主键）
   */
  recordId: number;
  /**
   * 标题
   */
  title?: string;
  /**
   * 关联预警台账表的预警编号
   */
  warningId?: string;
};

/** 获取事件预警列表 */
export const getAccidentAlarmList = (
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) => {
  return requestClientGridPage.get<{
    items: CmWarningRegister[];
    total: number;
  }>('/cm-warning-register', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
};

/** 获取事件预警详情 */
export const getAccidentAlarmDetail = (id: string) => {
  return requestClient.get<CmWarningRegister>(
    `/cm-warning-register/detail/${id}`,
  );
};

/** 新增事件预警 */
export const addAccidentAlarm = (data: CmWarningRegister) => {
  return requestClient.post<CmWarningRegister>('/cm-warning-register', data);
};

/** 更新事件预警 */
export const updateAccidentAlarm = (data: CmWarningRegister) => {
  return requestClient.put<CmWarningRegister>(`/cm-warning-register`, data);
};

/** 删除事件预警 */
export const deleteAccidentAlarm = (id: string) => {
  return requestClient.delete<CmWarningRegister>(`/cm-warning-register/${id}`);
};

/** 获取事件预警统计数据 */
export const getAccidentAlarmCountApi = () => {
  return requestClient.post<{
    handleRate: number;
    totalCount: number;
    typeMap: Record<string, number>;
  }>('/cm-warning-register/statistics');
};
