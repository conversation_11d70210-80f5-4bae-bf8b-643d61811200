import type { EventNotification } from '#/api/core/accident-alarm/notice';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-29
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\accident-alarm\notice\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { PriorityMap, StatusMap } from '#/data-model/comm';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '事件标题',
    },
    {
      fieldName: 'status',
      label: '事件状态',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EventNotification> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'eventTitle',
      title: '事件标题',
    },
    {
      field: 'eventType',
      title: '事件类型',
    },
    {
      field: 'eventLevel',
      title: '事件等级',
    },
    {
      field: 'priority',
      title: '优先级',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.priority;
        return [PriorityMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    {
      field: 'eventLocation',
      title: '发生地点',
    },
    {
      field: 'occurrenceTime',
      title: '发生时间',
      customType: 'date',
    },
    {
      field: 'processingStatus',
      title: '状态',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.processingStatus;
        return [StatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    // 事件标题 事件类型 事件等级 优先级 发生地点 发生时间 事件描述 影响范围 初步原因分析 已应对措施 事件状态 上报人姓名 上报人的部门 联系电话 电子邮箱 接收人
    {
      fieldName: 'eventTitle',
      label: '事件标题',
      rules: 'required',
    },
    {
      fieldName: 'eventType',
      label: '事件类型',
      rules: 'required',
    },
    {
      fieldName: 'eventLevel',
      label: '事件等级',
      rules: 'required',
    },
    {
      fieldName: 'priority',
      label: '优先级',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(PriorityMap),
      },
    },
    {
      fieldName: 'eventLocation',
      label: '发生地点',
      rules: 'required',
    },
    {
      fieldName: 'occurrenceTime',
      label: '发生时间',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        showTime: true,
      },
    },
    {
      fieldName: 'eventDescription',
      label: '事件描述',
      rules: 'required',
      component: 'Textarea',
    },
    {
      fieldName: 'impactScope',
      label: '影响范围',
      rules: 'required',
      component: 'Textarea',
    },
    {
      fieldName: 'preliminaryCause',
      label: '初步原因分析',
      rules: 'required',
      component: 'Textarea',
    },
    {
      fieldName: 'measuresTaken',
      label: '已应对措施',
      rules: 'required',
      component: 'Textarea',
    },
    {
      fieldName: 'notificationRecipients',
      label: '接收人',
      rules: 'required',
      component: 'Select',
      componentProps: {
        mode: 'tags',
      },
    },
    {
      fieldName: 'processingStatus',
      label: '事件状态',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },
    {
      fieldName: 'reporterName',
      label: '上报人姓名',
      rules: 'required',
    },
    {
      fieldName: 'reporterDepartment',
      label: '上报人部门',
      rules: 'required',
    },
    {
      fieldName: 'reporterPhone',
      label: '上报人电话',
      rules: 'required',
    },
  ];
}
