import { requestClient } from '#/api/request';

/**
 * 能源单价
 * EnPrice
 */
export type EnPrice = {
  createTime: string;
  effectiveDate: string;
  expiryDate: string;
  mediumId: string;
  periodTypeId: number;
  price: number;
  priceId: string;
  status: number;
  updateTime: string;
};

/**
 * 能源单价 DTO（包含关联数据）
 * PriceDTO
 */
export type PriceDTO = {
  createTime: string;
  effectiveDate: string;
  expiryDate: string;
  mediumId: string;
  mediumName: string;
  periodName: string;
  periodTypeId: number;
  price: number;
  priceId: string;
  status: number;
  updateTime: string;
};

/**
 * 获取能源单价列表
 */
export async function getEnergyPriceListApi(params?: {
  endTime?: string;
  mediumId?: string;
  periodTypeId?: number;
  startTime?: string;
}) {
  return requestClient.get<PriceDTO[]>('/energyManagementBaseData/price/list', {
    params,
  });
}

/**
 * 新增能源单价
 */
export async function addEnergyPriceApi(data: EnPrice) {
  return requestClient.post<EnPrice>('/energyManagementBaseData/price', data);
}

/**
 * 修改能源单价
 */
export async function editEnergyPriceApi(data: EnPrice) {
  return requestClient.put<EnPrice>('/energyManagementBaseData/price', data);
}

/**
 * 删除能源单价
 */
export async function deleteEnergyPriceApi(priceId: string) {
  return requestClient.delete<string>(
    `/energyManagementBaseData/price/${priceId}`,
  );
}
