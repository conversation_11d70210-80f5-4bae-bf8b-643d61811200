/*
 * @Author: Strayer
 * @Date: 2025-01-27
 * @LastEditors: Strayer
 * @LastEditTime: 2025-01-27
 * @Description: 能源介质类型管理API
 * @FilePath: \energy-web\apps\web-antd\src\api\core\energy-management\energy-medium-type.ts
 */

import { requestClient } from '#/api/request';

/**
 * 能源介质类型
 *
 * EnMediumType
 */
export type EnMediumType = {
  /**
   * 创建时间戳（创建时自动设置）
   */
  createTime?: string;
  /**
   * Font Awesome 图标类
   */
  iconClasses?: string;
  /**
   * 能源介质缩写代码
   */
  mediumCode?: string;
  /**
   * 唯一标识能源介质
   */
  mediumId?: string;
  /**
   * 能源介质名称
   */
  mediumName?: string;
  /**
   * 状态：1=启用，0=禁用
   */
  status?: number;
  /**
   * 能源介质计量单位
   */
  unit?: string;
  /**
   * 更新时间戳（更新时自动设置）
   */
  updateTime?: string;
};

/**
 * @returns 能源介质类型列表
 */
export async function getEnergyMediumTypeListApi(search: {
  mediumCode?: string;
  mediumName?: string;
  status?: number;
}) {
  return requestClient.get<EnMediumType[]>(
    '/energyManagementBaseData/mediumType/list',
    {
      params: {
        ...search,
      },
    },
  );
}

/**
 * 新增能源介质类型
 */
export async function addEnergyMediumTypeApi(data: EnMediumType) {
  return requestClient.post<EnMediumType>(
    '/energyManagementBaseData/mediumType',
    data,
  );
}

/**
 * 修改能源介质类型
 */
export async function editEnergyMediumTypeApi(data: EnMediumType) {
  return requestClient.put<EnMediumType>(
    '/energyManagementBaseData/mediumType',
    data,
  );
}

/**
 * 删除能源介质类型
 */
export async function deleteEnergyMediumTypeApi(id: string) {
  return requestClient.delete<string>(
    `/energyManagementBaseData/mediumType/${id}`,
  );
}
