/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-15
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\energy-management\alarm-config\data.ts
 */
import type { WarningConfigDTO } from '#/api/core/energy-management/alarm-config';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { getAreaListApi } from '#/api/core/energy-management/area-manage';
import { getDeviceListApi } from '#/api/core/energy-management/device-manage';

// 用能类型map
export const UsageTypeMap: Record<number, { label: string; value: number }> = {
  1: {
    label: '水',
    value: 1,
  },
  2: {
    label: '电',
    value: 2,
  },
  3: {
    label: '标煤',
    value: 3,
  },
  // 4: {
  //   label: '碳排放',
  //   value: 4,
  // },
};

// 统计周期map
export const PeriodMap: Record<number, { label: string; value: number }> = {
  1: {
    label: '日',
    value: 1,
  },
  2: {
    label: '月',
    value: 2,
  },
  3: {
    label: '年',
    value: 3,
  },
};

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<WarningConfigDTO> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    // 用能区域
    {
      field: 'areaName',
      title: '用能区域',
    },
    // 用能设备
    {
      field: 'deviceName',
      title: '用能设备',
    },
    // 用量类型
    {
      field: 'type',
      title: '用能类型',
      formatter: ({ cellValue }: { cellValue: number }) => {
        return UsageTypeMap[cellValue]?.label ?? cellValue ?? '';
      },
    },
    // 统计周期
    {
      field: 'period',
      title: '统计周期',
      formatter: ({ cellValue }: { cellValue: number }) => {
        return PeriodMap[cellValue]?.label ?? cellValue ?? '';
      },
    },
    // 标准值
    {
      field: 'value',
      title: '标准值',
    },
    // 告警偏差范围
    {
      field: 'range',
      title: '告警偏差范围',
      formatter: ({ cellValue }: { cellValue: number }) => {
        return `±${cellValue ?? '--'}%`;
      },
    },
    // 干系人
    {
      field: 'name',
      title: '干系人',
    },

    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'areaId',
      label: '用能区域',
      rules: 'required',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getAreaListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              areaName: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.areaName ?? '',
            value: item.areaId,
          }));
        },
      },
    },
    {
      fieldName: 'deviceId',
      label: '用能设备',
      rules: 'required',
      component: 'ApiSelect',
      custom: {
        fetchNeedFormData: true,
        fetchRemoteOptions: async ({
          keyword = '',
          formData,
        }: Record<string, any>) => {
          const tableDataAll = await getDeviceListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              deviceName: keyword,
              areaId: formData?.areaId,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.deviceName ?? '',
            value: item.deviceId,
          }));
        },
      },
    },
    {
      fieldName: 'type',
      label: '用能类型',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(UsageTypeMap),
      },
    },
    {
      fieldName: 'period',
      label: '统计周期',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(PeriodMap),
      },
    },
    // 标准值
    {
      fieldName: 'value',
      label: '标准值',
      rules: 'required',
      component: 'InputNumber',
    },
    // 偏差范围
    {
      fieldName: 'range',
      label: '告警偏差范围(%)',
      rules: 'required',
      component: 'InputNumber',
    },
    // 干系人
    {
      fieldName: 'name',
      label: '干系人',
      rules: 'required',
    },
  ];
}
