<!--
 * @Author: Strayer
 * @Date: 2025-08-04
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-18
 * @Description: 表格顶部搜索
-->
<script lang="ts" setup>
import type { CustomFormSchema } from '#/components/form-modal';

import { onMounted, shallowRef, watch } from 'vue';

import { Card } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { formatFormOption } from '#/components/form-modal';

const props = defineProps<{
  iniForm?: Record<string, any>;
  schema: CustomFormSchema[];
}>();

const emit = defineEmits<{
  (e: 'search', value: Record<string, any>): void;
}>();

const [QueryForm, formApi] = useVbenForm({
  // 默认展开
  collapsed: false,
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  handleReset: onReset,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: formatFormOption(props.schema ?? [], {
    fetchingMap: shallowRef({}),
    keywordMap: shallowRef({}),
  }) as any,
  submitButtonOptions: {
    content: '查询',
  },
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  actionWrapperClass: 'customTableTopSearchAction',
});

onMounted(() => {
  watch(
    () => props.iniForm,
    (value) => {
      if (value) formApi.setValues({ ...value });
    },
    { immediate: true },
  );
});

function onSubmit(values: Record<string, any>) {
  emit('search', values);
}
function onReset() {
  if (props.iniForm) formApi.setValues({ ...props.iniForm });
  else formApi.resetForm();
  emit('search', { ...props.iniForm });
}
</script>

<template>
  <div class="schedule-search">
    <Card class="mb-4">
      <QueryForm />
    </Card>
  </div>
</template>
<style lang="css">
.schedule-search .customTableTopSearchAction {
  padding-bottom: 0;
}
</style>
