import type { EmergencyEquipment } from '#/api/core/emergency/equipment';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-23
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\equipment\data.ts
 */
import type { StorePoint } from '#/api/core/emergency/store-point';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { EmergencyMaterialStatusMap } from '#/api/core/emergency/material';

// 顶部搜索表单
export function getTopSearchScheme(
  storePointList: StorePoint[],
): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '装备名称',
    },
    {
      fieldName: 'code',
      label: '装备编码',
    },
    {
      fieldName: 'teamId',
      label: '所属单位',
    },
    {
      fieldName: 'typeId',
      label: '装备类型',
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        options: Object.keys(EmergencyMaterialStatusMap).map((item) => ({
          label: EmergencyMaterialStatusMap[item],
          value: item,
        })),
      },
    },
    {
      fieldName: 'responsibleId',
      label: '责任人',
    },
    {
      fieldName: 'locationId',
      label: '存放位置',
      component: 'Select',
      componentProps: {
        options: storePointList.map((item) => ({
          label: item.locationName,
          value: item.locationId,
        })),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'timeRange',
      label: '入库日期范围',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<any> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'equipmentId',
      title: '装备编码',
    },
    {
      field: 'name',
      title: '装备名称',
    },
    {
      field: 'unitId',
      title: '所属单位',
    },
    {
      field: 'typeId',
      title: '装备类型',
    },
    // 责任人
    {
      field: 'responsibleId',
      title: '责任人',
    },
    {
      field: 'status',
      title: '状态',
      formatter: ({ cellValue }) =>
        EmergencyMaterialStatusMap[cellValue as string] ?? cellValue ?? '',
    },
    {
      field: 'storageDate',
      title: '入库日期',
      customType: 'dateDay',
      formatter: (param: { row: EmergencyEquipment }) =>
        param.row.cmInventory?.storageDate ?? '',
    },

    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(
  storePointList: StorePoint[],
): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '装备名称',
      rules: 'required',
    },
    {
      fieldName: 'typeId',
      label: '装备类型',
      rules: 'required',
    },
    {
      fieldName: 'unitId',
      label: '所属单位',
      rules: 'required',
    },
    {
      fieldName: 'specification',
      label: '规格型号',
      rules: 'required',
    },
    {
      label: '品牌',
      fieldName: 'brand',
    },
    {
      label: '生产厂家',
      fieldName: 'manufacturer',
      rules: 'required',
    },
    {
      label: '计量单位',
      fieldName: 'unit',
      rules: 'required',
    },
    {
      label: '责任人',
      fieldName: 'responsibleId',
      rules: 'required',
    },
    {
      label: '装备图片',
      fieldName: 'cmEquipmentImageList',
      component: 'UploadImg',
      custom: {
        uploadMultiple: true,
        fileDataType: 'fileList',
      },
    },
    {
      label: '存放位置',
      fieldName: 'cmInventoryLocationId',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: storePointList.map((item) => ({
          label: item.locationName,
          value: item.locationId,
        })),
      },
    },
    {
      label: '库存数量',
      fieldName: 'cmInventoryInfoCurrentQuantity',
      rules: 'required',
      component: 'InputNumber',
    },
    {
      label: '安全库存',
      fieldName: 'cmInventoryInfoSafetyQuantity',
      component: 'InputNumber',
    },
    {
      label: '入库日期',
      fieldName: 'cmInventoryStorageDate',
      component: 'DatePicker',
      rules: 'required',
    },
    {
      label: '购买日期',
      fieldName: 'cmInventoryPurchaseDate',
      component: 'DatePicker',
    },
    // 保质期(月)
    {
      label: '保质期(月)',
      fieldName: 'cmInventoryShelfLife',
      component: 'InputNumber',
    },
    {
      label: '状态',
      fieldName: 'status',
      component: 'Select',
      rules: 'required',
      componentProps: {
        options: Object.keys(EmergencyMaterialStatusMap).map((item) => ({
          label: EmergencyMaterialStatusMap[item],
          value: item,
        })),
      },
    },
    {
      label: '采购价格',
      fieldName: 'cmInventoryPurchasePrice',
      component: 'InputNumber',
    },
    {
      label: '供应商',
      fieldName: 'cmInventorySupplier',
    },
    {
      label: '备注',
      fieldName: 'remarks',
      component: 'Textarea',
    },
  ];
}
