/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-20
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\energy-management\carbon-emission-knowledge-base\data.ts
 */
import type { EnCarbonEmissionKnowledge } from '#/api/core/energy-management/carbon-emission-knowledge-base';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'nodeName',
      label: '名称',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EnCarbonEmissionKnowledge> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'nodeName',
      title: '名称',
    },
    {
      title: '更新时间',
      field: 'updateTime',
      customType: 'date',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'nodeName',
      label: '名称',
      rules: 'required',
    },
  ];
}
