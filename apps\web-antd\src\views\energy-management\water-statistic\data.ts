import type { WaterUsageRecordDTO } from '#/api/core/energy-management/water-statistic';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-15
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\energy-management\water-statistic\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { getAreaListApi } from '#/api/core/energy-management/area-manage';
import { getDeviceListApi } from '#/api/core/energy-management/device-manage';

import { DeviceStatusMap } from '../device-manage/data';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'recordDate',
      label: '日期',
      component: 'DatePicker',
      componentProps: {
        placeholder: '不填默认今日',
      },
    },
    // 区域
    {
      fieldName: 'areaId',
      label: '区域',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getAreaListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              areaName: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.areaName ?? '',
            value: item.areaId,
          }));
        },
      },
    },
    // 设备
    {
      fieldName: 'deviceId',
      label: '设备',
      component: 'ApiSelect',
      custom: {
        fetchNeedFormData: true,
        fetchRemoteOptions: async ({
          keyword = '',
          formData,
        }: Record<string, any>) => {
          const tableDataAll = await getDeviceListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              deviceName: keyword,
              areaId: formData?.areaId,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.deviceName ?? '',
            value: item.deviceId,
          }));
        },
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<WaterUsageRecordDTO> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'deviceCode',
      title: '设备编号',
    },
    // 设备名称
    {
      field: 'deviceName',
      title: '设备名称',
    },
    // 区域名称
    {
      field: 'areaName',
      title: '所属区域',
    },
    // 用水量
    {
      field: 'waterUsage',
      title: '今日用量(m³)',
    },
    // 设备状态
    {
      field: 'status',
      title: '状态',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.status;
        return [DeviceStatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    // 最后更新
    {
      field: 'updateTime',
      title: '最后更新',
      customType: 'date',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'areaId',
      label: '区域',
      rules: 'required',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getAreaListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              areaName: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.areaName ?? '',
            value: item.areaId,
          }));
        },
      },
    },
    // 设备
    {
      fieldName: 'deviceId',
      label: '设备',
      rules: 'required',
      component: 'ApiSelect',
      custom: {
        fetchNeedFormData: true,
        fetchRemoteOptions: async ({
          keyword = '',
          formData,
        }: Record<string, any>) => {
          const tableDataAll = await getDeviceListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              deviceName: keyword,
              areaId: formData?.areaId,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.deviceName ?? '',
            value: item.deviceId,
          }));
        },
      },
    },
    // 用水量
    {
      fieldName: 'waterUsage',
      label: '用水量(m³)',
      rules: 'required',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入用水量',
        min: 0,
      },
    },
    // 记录日期
    {
      fieldName: 'recordDate',
      label: '记录日期',
      rules: 'required',
      component: 'DatePicker',
    },
    // 记录的时间范围
    {
      fieldName: 'timeRange',
      label: '记录的时间范围',
      rules: 'required',
      component: 'RangePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH',
      },
      custom: {
        rangePicker0: 'startTime',
        rangePicker1: 'endTime',
      },
    },
  ];
}
