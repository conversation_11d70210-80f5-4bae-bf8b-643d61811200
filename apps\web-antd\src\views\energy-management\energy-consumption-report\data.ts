import type { EnArea } from '#/api/core/energy-management/area-manage';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { shallowRef } from 'vue';

// 顶部搜索表单配置
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'date',
      label: '日期',
      component: 'DatePicker',
      labelWidth: 80,
      componentProps: {
        picker: 'month',
        allowClear: false,
      },
    },
  ];
}

// 表格列配置
export function getColumnsScheme(): CustomColumnSchema<any> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'reportCode', title: '报表编码' },
    { field: 'reportDate', title: '记录日期', customType: 'dateDay' },
    { field: 'waterUsage', title: '总用水量(m³)' },
    { field: 'electricityUsage', title: '总用电量(kWh)' },
    { field: 'waterPrice', title: '用水成本(元)' },
    { field: 'electricityPrice', title: '用电成本(元)' },
    { field: 'action', title: '操作', width: 250 },
  ];
}

// 新建/编辑表单项配置
export function getFormOption(areaList: EnArea[]): CustomFormSchema[] {
  return [
    {
      fieldName: 'reportCode',
      label: '报表编码',
      rules: 'required',
      labelWidth: 130,
    },
    {
      fieldName: 'waterUsage',
      label: '总用水量(m³)',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'electricityUsage',
      label: '总用电量(kWh)',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'waterPrice',
      label: '用水成本(元)',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'electricityPrice',
      label: '用电成本(元)',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'waterUsageRatio',
      label: '用水量环比变化',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'electricityUsageRatio',
      label: '用电量环比变化',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'waterPriceRatio',
      label: '用水费用环比变化',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'electricityPriceRatio',
      label: '用电费用环比变化',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },

    // factorPriceDetail 用能成本详情
    {
      fieldName: 'detail_totalPrice',
      label: '总费用',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'detail_totalPriceRatio',
      rules: 'required',
      label: '总费用环比变化',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'detail_waterUsage',
      label: '用水量(吨)',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'detail_electricityUsage',
      label: '用电量(kWh)',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'detail_waterPrice',
      label: '用水单价',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'detail_electricityPrice',
      label: '用电单价',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'detail_waterTotalPrice',
      label: '用水总成本',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'detail_electricityTotalPrice',
      label: '用电总成本',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'detail_waterPriceRatio',
      label: '用水费用环比变化',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'detail_electricityPriceRatio',
      label: '用电费用环比变化',
      rules: 'required',
      component: 'InputNumber',
      labelWidth: 130,
    },
    {
      fieldName: 'waterReportDetailList',
      label: '用水计量',
      labelWidth: 80,
      component: 'Add',
      custom: {
        addFormConfig: {
          wrapperClass: 'grid-cols-4',
        },
        addObj: [
          {
            fieldName: 'areaId',
            label: '区域',
            rules: 'required',
            component: 'Select',
            labelWidth: 50,
            componentProps: {
              placeholder: '请选择区域',
              options: areaList.map((item) => ({
                label: item.areaName,
                value: item.areaId,
              })),
            },
          },
          {
            fieldName: 'usage',
            label: '用量',
            labelWidth: 50,
            rules: 'required',
            component: 'InputNumber',
          },
          {
            fieldName: 'usagePercentage',
            label: '占比',
            labelWidth: 50,
            rules: 'required',
            component: 'InputNumber',
          },
          {
            fieldName: 'usageRatio',
            label: '环比变化',
            labelWidth: 70,
            component: 'InputNumber',
          },
        ],
      },
    },
    {
      fieldName: 'electricityReportDetailList',
      label: '用电计量',
      labelWidth: 80,
      component: 'Add',
      custom: {
        addFormConfig: {
          wrapperClass: 'grid-cols-4',
        },
        addObj: [
          {
            fieldName: 'areaId',
            label: '区域',
            rules: 'required',
            component: 'Select',
            labelWidth: 50,
            componentProps: {
              placeholder: '请选择区域',
              options: areaList.map((item) => ({
                label: item.areaName,
                value: item.areaId,
              })),
            },
          },
          {
            fieldName: 'usage',
            label: '用量',
            rules: 'required',
            component: 'InputNumber',
            labelWidth: 50,
          },
          {
            fieldName: 'usagePercentage',
            label: '占比',
            rules: 'required',
            component: 'InputNumber',
            labelWidth: 50,
          },
          {
            fieldName: 'usageRatio',
            label: '环比变化',
            component: 'InputNumber',
            labelWidth: 70,
          },
        ],
      },
    },
  ];
}

export const currentRow = shallowRef<any>({});

export const reportDataMap = shallowRef<Record<string, any>>({});
