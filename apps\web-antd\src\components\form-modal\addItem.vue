<!--
 * @Author: Strayer
 * @Date: 2025-07-26
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-31
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\components\form-modal\addItem.vue
-->
<script lang="ts" setup>
import type { Ref } from 'vue';

import type { CustomFormSchema } from '.';

import { h, nextTick, ref, watch } from 'vue';

import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { Button } from 'ant-design-vue';
import { flat } from 'remeda';

import { useVbenForm } from '#/adapter/form';

import { formatFormOption } from '.';

type LocalCustomFormSchema = CustomFormSchema & {
  realFieldName: string;
};

const props = defineProps<{
  itemOption: CustomFormSchema;
}>();

const childrenFormOption: Ref<LocalCustomFormSchema[][]> = ref([[]]);

const [AddForm, addFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 在label后显示一个冒号
    colon: true,
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  // layout: 'horizontal',
  schema: flat(childrenFormOption.value) as any,
  showDefaultActions: false,
  ...props.itemOption.custom?.addFormConfig,
});

watch(
  () => childrenFormOption.value,
  (schema: any) => {
    addFormApi.setState(() => ({
      schema: flat(schema) as any,
    }));
  },
  { deep: true },
);

let lastIndex = 0;
function setData(value: any[]) {
  const formOption: LocalCustomFormSchema[][] = [];
  const formData: any = {};

  for (const [i, element] of value.entries()) {
    formOption.push([]);

    const addObjArr = formatFormOption(props.itemOption.custom?.addObj ?? [], {
      keywordMap: ref({}),
      fetchingMap: ref({}),
    });
    for (let j = 0; j < (addObjArr.length ?? 0); j++) {
      const item = addObjArr[j];
      if (!item) continue;
      const fieldName = item.fieldName + i;

      formData[fieldName] = element[item.fieldName];

      const obj = {
        ...item,
        realFieldName: item.fieldName,
        fieldName,
      };
      if (j === addObjArr.length - 1) {
        obj.suffix = () =>
          h(Button, {
            type: 'link',
            danger: true,
            onClick: () => remove(i),
            icon: h(MinusCircleOutlined),
          });
      }
      formOption[i]!.push(obj);
    }
  }
  lastIndex = value.length;
  childrenFormOption.value = formOption;
  nextTick(() => {
    addFormApi.setValues(formData);
  });
}

function add() {
  const addObjArr = formatFormOption(props.itemOption.custom?.addObj ?? [], {
    keywordMap: ref({}),
    fetchingMap: ref({}),
  });

  const currentNum = childrenFormOption.value.length;
  childrenFormOption.value.push(
    addObjArr.map((item, index) => ({
      ...item,
      realFieldName: item.fieldName,
      fieldName: item.fieldName + lastIndex,
      suffix:
        index === addObjArr.length - 1
          ? () =>
              h(Button, {
                type: 'link',
                danger: true,
                icon: h(MinusCircleOutlined),
                onClick: () => remove(currentNum),
              })
          : undefined,
    })) ?? [],
  );
  lastIndex++;
}

function remove(index: number) {
  childrenFormOption.value.splice(index, 1);
}

function getValue() {
  return addFormApi.getValues().then((res) => {
    const result: any[] = [];
    for (let i = 0; i < childrenFormOption.value.length; i++) {
      const obj: any = {};
      for (const item of childrenFormOption.value[i]!) {
        obj[item.realFieldName] = res[item.fieldName];
      }
      result.push(obj);
    }

    return result;
  });
}

defineExpose({
  setData,
  addFormApi,
  getValue,
});
</script>

<template>
  <div class="w-full">
    <AddForm />
    <div class="w-full text-right">
      <Button
        type="primary"
        shape="circle"
        :icon="h(PlusOutlined)"
        @click="add"
      />
    </div>
  </div>
</template>
