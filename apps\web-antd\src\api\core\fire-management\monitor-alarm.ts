/*
 * @Author: Strayer
 * @Date: 2025-07-25
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-25
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\api\core\fire-management\monitor-alarm.ts
 */
import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * 设备
 */
export type FfMonitoringAlarm = {
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 设备唯一标识（主键）
   */
  deviceId: string;
  /**
   * 设备名称（如：烟雾探测器001）
   */
  deviceName?: string;
  /**
   * 设备类型代码（如：SMOKE-01）
   */
  deviceType?: string;
  /**
   * 设备安装日期
   */
  installDate?: string;
  /**
   * 设备安装位置（如：二楼东侧走廊）
   */
  installLocation?: string;
  /**
   * 设备维护责任人
   */
  responsiblePerson?: string;
  /**
   * 记录更新时间（自动更新）
   */
  updateTime?: string;
};

/**
 * 告警对象
 */
export type FfEquipmentAlarmRecord = {
  /**
   * 告警详情描述
   */
  alarmDetail?: string;
  /**
   * 告警唯一标识
   */
  alarmId: string;
  /**
   * 告警发生时间（默认当前时间）
   */
  alarmTime?: string;
  /**
   * 告警类型代码
   */
  alarmType?: string;
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 设备唯一标识（外键关联消防监测报警设备表）
   */
  deviceId: string;
  /**
   * 处理状态（pending：待处理，processed：已处理）
   */
  processStatus?: string;
  /**
   * 处理完成时间（处理完成后更新）
   */
  processTime?: string;
};

/** 获取设备列表 */
export async function getMonitoringAlarmListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: FfMonitoringAlarm[];
    total: number;
  }>('/fireManagement/fireDetectionAndAlarm/page', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 新增设备 */
export async function addMonitoringAlarmApi(data: FfMonitoringAlarm) {
  return requestClient.post<FfMonitoringAlarm>(
    '/fireManagement/fireDetectionAndAlarm',
    data,
  );
}

/** 修改设备 */
export async function editMonitoringAlarmApi(data: FfMonitoringAlarm) {
  return requestClient.put<FfMonitoringAlarm>(
    '/fireManagement/fireDetectionAndAlarm',
    data,
  );
}

/** 删除设备 */
export async function deleteMonitoringAlarmApi(deviceId: string) {
  return requestClient.delete<FfMonitoringAlarm>(
    `/fireManagement/fireDetectionAndAlarm/${deviceId}`,
  );
}

/** 获取告警列表 */
export async function getAlarmLogListApi(
  deviceId: string,
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: FfEquipmentAlarmRecord[];
    total: number;
  }>('/fireManagement/fireDetectionAndAlarm/equipmentAlarmRecord/page', {
    params: {
      deviceId,
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 新增告警 */
export async function addAlarmLogApi(data: FfEquipmentAlarmRecord) {
  return requestClient.post<FfEquipmentAlarmRecord>(
    '/fireManagement/fireDetectionAndAlarm/equipmentAlarmRecord',
    data,
  );
}

/** 修改告警 */
export async function editAlarmLogApi(data: FfEquipmentAlarmRecord) {
  return requestClient.put<FfEquipmentAlarmRecord>(
    '/fireManagement/fireDetectionAndAlarm/equipmentAlarmRecord',
    data,
  );
}

/** 删除告警 */
export async function deleteAlarmLogApi(alarmId: string) {
  return requestClient.delete<FfEquipmentAlarmRecord>(
    `/fireManagement/fireDetectionAndAlarm/equipmentAlarmRecord/${alarmId}`,
  );
}
