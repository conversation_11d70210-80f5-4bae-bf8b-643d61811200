import type { CmDutyLog } from '#/api/core/emergency/duty-log';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-25
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\duty-log\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import {
  DutyLogStatusMap,
  DutyLogTypeMap,
} from '#/api/core/emergency/duty-log';
import { getOrganizationPersonListApi } from '#/api/core/emergency/organization';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'dateRange',
      label: '日期范围',
      component: 'RangePicker',
    },
    {
      label: '日志类型',
      fieldName: 'type',
      component: 'Select',
      componentProps: {
        options: Object.values(DutyLogTypeMap),
      },
    },
    {
      label: '值班人员',
      fieldName: 'person',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getOrganizationPersonListApi(
            {
              currentPage: 1,
              pageSize: 100,
            },
            {
              name: keyword,
            },
          );
          return tableDataAll.items.map((item) => ({
            label: item.name ?? '',
            value: item.id,
          }));
        },
      },
    },
    {
      label: '状态',
      fieldName: 'state',
      component: 'Select',
      componentProps: {
        options: Object.values(DutyLogStatusMap),
      },
    },
    {
      label: '关键词搜索',
      fieldName: 'word',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CmDutyLog> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'logId', title: 'ID', visible: false },
    {
      field: 'logType',
      title: '日志类型',
      formatter: ({ cellValue }) =>
        DutyLogTypeMap[cellValue as number]?.label ?? cellValue ?? '',
    },
    { field: 'title', title: '标题' },
    {
      field: 'staffId',
      title: '值班人员',
      customType: 'userInfo',
      custom: {
        userInfo: {
          name: (row: CmDutyLog) => row.personnel?.name ?? '',
          img: (row: CmDutyLog) => row.personnel?.photoPath ?? '',
          desc: (row: CmDutyLog) => row.personnel?.position ?? '',
        },
      },
    },
    { field: 'recordTime', title: '记录时间', customType: 'date' },
    {
      field: 'status',
      title: '状态',
      formatter: ({ cellValue }) =>
        DutyLogStatusMap[cellValue as number]?.label ?? cellValue ?? '',
    },
    {
      field: 'action',
      title: '操作',
      width: 320,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      label: '日志类型',
      fieldName: 'logType',
      component: 'Select',
      componentProps: {
        options: Object.values(DutyLogTypeMap),
      },
      rules: 'required',
    },
    {
      label: '标题',
      fieldName: 'title',
      rules: 'required',
    },
    {
      label: '值班人员',
      fieldName: 'staffId',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getOrganizationPersonListApi(
            {
              currentPage: 1,
              pageSize: 100,
            },
            {
              name: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.name ?? '',
            value: item.id,
          }));
        },
      },
      rules: 'required',
    },
    {
      label: '记录时间',
      fieldName: 'recordTime',
      component: 'DatePicker',
      rules: 'required',
      componentProps: {
        showTime: true,
      },
    },
    {
      label: '状态',
      fieldName: 'status',
      component: 'Select',
      componentProps: {
        options: Object.values(DutyLogStatusMap),
      },
      rules: 'required',
    },
    {
      label: '详细描述',
      fieldName: 'description',
      component: 'Textarea',
      rules: 'required',
    },
    {
      label: '处理措施',
      fieldName: 'measures',
      component: 'Textarea',
    },
    {
      label: '附件',
      fieldName: 'logAttachment',
      component: 'Upload',
    },
  ];
}
