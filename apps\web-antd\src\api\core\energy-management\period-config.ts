import { requestClient } from '#/api/request';

/**
 * 时段配置
 * EnPeriodConfig
 */
export type EnPeriodConfig = {
  applyRange: string;
  createTime: string;
  endTime: string;
  periodId: string;
  periodTypeId: number;
  startTime: string;
  status: number;
  updateTime: string;
};

/**
 * 获取时段配置列表
 */
export async function getPeriodConfigListApi(params?: {
  periodTypeId?: number;
  status?: number;
}) {
  return requestClient.get<EnPeriodConfig[]>(
    '/energyManagementBaseData/periodConfig/list',
    {
      params,
    },
  );
}

/**
 * 新增时段配置
 */
export async function addPeriodConfigApi(data: EnPeriodConfig) {
  return requestClient.post<EnPeriodConfig>(
    '/energyManagementBaseData/periodConfig',
    data,
  );
}

/**
 * 修改时段配置
 */
export async function editPeriodConfigApi(data: EnPeriodConfig) {
  return requestClient.put<EnPeriodConfig>(
    '/energyManagementBaseData/periodConfig',
    data,
  );
}

/**
 * 删除时段配置
 */
export async function deletePeriodConfigApi(periodId: string) {
  return requestClient.delete<string>(
    `/energyManagementBaseData/periodConfig/${periodId}`,
  );
}
