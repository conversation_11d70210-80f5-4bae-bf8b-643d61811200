<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, nextTick, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, Divider, message, Tag, Tooltip } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addEmergencyScheduleApi,
  getEmergencyScheduleDetailApi,
  getEmergencyScheduleListApi,
} from '#/api/core/emergency/schedule';
import GridForm from '#/components/grid-form/index.vue';
import TableTopSearch from '#/components/tableTopSearch.vue';

import {
  getColumnsScheme,
  getFormOption,
  getTopSearchScheme,
  shiftMap,
} from './data';
import Detail from './detail.vue';

const currentDateObj = shallowRef(dayjs());

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const detailRef = ref<ComponentExposed<typeof Detail>>();

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<any>> = shallowRef(
  getColumnsScheme(currentDateObj.value),
);
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

const searchFormValue = shallowRef<Record<string, any>>({});
/**
 * @description: 获取表格数据
 */
async function query(param: VxeGridPropTypes.ProxyAjaxQueryParams<any>) {
  searchFormValue.value.year = currentDateObj.value.year();
  searchFormValue.value.month = currentDateObj.value.month() + 1;

  const res = await getEmergencyScheduleListApi(
    param.page,
    searchFormValue.value,
  );

  return res;
}

// /**
//  * @description: 删除
//  */
// function deletePersonnel(row: any) {
//   return deleteEmergencyExpertApi(row.expertId).then(() => {
//     message.success('删除成功');
//     gridFormRef.value?.gridApi.reload();
//   });
// }

// /**
//  * @description: 编辑
//  */
// function editPersonnel(row: any) {
//   gridFormRef.value?.gridApi.setLoading(true);
//   getEmergencyExpertDetailApi(row.expertId)
//     .then((res) => {
//       gridFormRef.value?.formModalRef?.open({
//         title: '编辑应急排班',
//         formData: res,
//         meta: { type: 'edit', raw: row },
//       });
//     })
//     .finally(() => {
//       gridFormRef.value?.gridApi.setLoading(false);
//     });
// }

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  if (param.meta.type === 'create') {
    Reflect.deleteProperty(param.formData, 'dateRange');

    addEmergencyScheduleApi(param.formData)
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  }
  // else if (param.meta.type === 'edit')
  //   editEmergencyExpertApi({
  //     ...param.raw,
  //     ...param.formData,
  //   })
  //     .then(() => {
  //       message.success('更新成功');
  //       gridFormRef.value?.gridApi.query();
  //       return true;
  //     })
  //     .catch(() => false);

  return Promise.resolve(true);
}

/** 点击tag */
function handleTagClick(item: any) {
  getEmergencyScheduleDetailApi(item.scheduleId).then((res) => {
    detailRef.value?.open(res);
  });
}

// 顶部搜索点击
function handleSearch(values: Record<string, any>) {
  hadLoad.value = false;
  searchFormValue.value = values;
  currentDateObj.value = values.date ?? dayjs();
  columnsScheme.value = getColumnsScheme(currentDateObj.value);

  nextTick(() => {
    hadLoad.value = true;
  });
}
</script>

<template>
  <Page auto-content-height>
    <TableTopSearch :schema="topSearchScheme" @search="handleSearch" />
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        :title="`${currentDateObj.year()}年${currentDateObj.month() + 1}月值班排班表`"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :submit="onSubmit"
        :disable-page="true"
      >
        <template #toolbar-actions>
          <Divider class="my-2" />
          <div class="flex gap-3 pl-6">
            <div v-for="item in Object.values(shiftMap)" :key="item.label">
              <Tag :color="item.color">{{ item.label }}</Tag>
              <span>{{ item.range }}</span>
            </div>
          </div>
        </template>
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增应急排班',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template
          v-for="item in columnsScheme.filter(
            (item) => item.customType === 'tag',
          )"
          :key="item.field"
          #[item.field]="param"
        >
          <Tooltip>
            <template #title>
              {{
                param.row[item.field]
                  ?.map((item1: any) => item1.value)
                  .join(' , ')
              }}
            </template>
            <template v-for="item1 in param.row[item.field] ?? []" :key="item1">
              <Tag
                :color="shiftMap[item1.value ?? '']?.color ?? 'purple'"
                class="cursor-pointer"
                @click="handleTagClick(item1)"
              >
                {{ item1.value }}
              </Tag>
            </template>
          </Tooltip>
        </template>
      </GridForm>
    </template>
    <Detail ref="detailRef" @had-edit="gridFormRef?.gridApi.reload()" />
  </Page>
</template>
