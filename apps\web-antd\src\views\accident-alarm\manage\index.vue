<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CmWarningRegister } from '#/api/core/accident-alarm/manage';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';
import type { StatisticItem } from '#/components/statistic-card.vue';

import { h, ref, shallowRef, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';

import { SvgCheckCircleIcon } from '@vben/icons';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addAccidentAlarm,
  deleteAccidentAlarm,
  getAccidentAlarmCountApi,
  getAccidentAlarmDetail,
  getAccidentAlarmList,
  updateAccidentAlarm,
} from '#/api/core/accident-alarm/manage';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';
import StatisticCard from '#/components/statistic-card.vue';

import {
  getColumnsScheme,
  getFormOption,
  getTopSearchScheme,
  LevelMap,
} from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<CmWarningRegister>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

const router = useRouter();

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<CmWarningRegister>,
  fromData: any,
) {
  if (fromData.dateRange) {
    fromData.beginDate = fromData.dateRange[0].startOf('day').toISOString();
    fromData.endDate = fromData.dateRange[1].endOf('day').toISOString();
    delete fromData.dateRange;
  }

  getCountData();

  return getAccidentAlarmList(param.page, fromData);
}

/**
 * @description: 删除
 */
function deleteWarning(row: CmWarningRegister) {
  return deleteAccidentAlarm(row.warningId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editWarning(row: CmWarningRegister) {
  gridFormRef.value?.gridApi.setLoading(true);
  getAccidentAlarmDetail(row.warningId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '编辑事故预警',
        formData: res,
        meta: { type: 'edit', raw: row },
      });
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: CmWarningRegister;
}): Promise<boolean> {
  if (param.meta.type === 'create') {
    param.formData.processingStatus = '处理中';
    addAccidentAlarm({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  } else if (param.meta.type === 'edit')
    updateAccidentAlarm({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}

const total = ref<StatisticItem[]>([]);

/** 获取统计数据 */
function getCountData() {
  getAccidentAlarmCountApi().then((res) => {
    total.value = [];

    for (const key of Object.keys(res.typeMap ?? {})) {
      total.value.push({
        color: LevelMap[key]?.color ?? '#3bce87',
        title: key,
        value: res.typeMap[key] ?? 0,
        svgComponent: LevelMap[key]?.component ?? SvgCheckCircleIcon,
        tag: LevelMap[key]?.tag ?? '',
      });
    }
    total.value.push({
      color: '#3bce87',
      title: '预警处理率',
      value: `${(res.handleRate ?? 0) * 100}%`,
      svgComponent: SvgCheckCircleIcon,
    });
  });
}
</script>

<template>
  <div class="p-4">
    <template v-if="hadLoad">
      <!-- 统计 -->
      <StatisticCard :total="total" />
      <!-- 表格 -->
      <GridForm
        ref="gridFormRef"
        title="事件预警列表"
        style="height: calc(100vh - 180px)"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增事件预警',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="editWarning(row)"> 编辑 </Button>
          <Button
            type="link"
            @click="
              router.push(`/accident-alarm/manage/detail/${row.warningId}`)
            "
          >
            处理
          </Button>
          <RemoveButton @confirm="deleteWarning(row)" />
        </template>
      </GridForm>
    </template>
  </div>
</template>
