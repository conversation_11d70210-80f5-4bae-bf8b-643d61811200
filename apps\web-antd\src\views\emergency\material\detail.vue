<script setup lang="ts">
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { EmergencyMaterial } from '#/api/core/emergency/material';
import type { StorePoint } from '#/api/core/emergency/store-point';

import { shallowRef, useTemplateRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Card, Image, ImagePreviewGroup } from 'ant-design-vue';
import dayjs from 'dayjs';

import { EmergencyMaterialStatusMap } from '#/api/core/emergency/material';
import QRcode from '#/components/QRcode/index.vue';

const QRcodeRef = useTemplateRef<ComponentExposed<typeof QRcode>>('QRcodeRef');

const materialObj = shallowRef<EmergencyMaterial>();

const storePointList = shallowRef<StorePoint[]>([]);

const [Modal, modalApi] = useVbenModal({
  footer: false,
  onCancel() {
    modalApi.close();
  },
});

function open(param: EmergencyMaterial, storePoint: StorePoint[]) {
  materialObj.value = param;
  storePointList.value = storePoint;
  modalApi.open();
}

defineExpose({
  open,
});
</script>

<template>
  <Modal class="w-[880px]" title="物资详情">
    <!-- 设备基本信息展示 -->
    <div v-if="materialObj">
      <Card title="基本信息" size="small">
        <Button
          class="absolute right-3 top-0.5"
          type="primary"
          @click="
            QRcodeRef?.open({
              qrContent: {
                text: `物资编码：${materialObj.supplyCode ?? ''}`,
              },
            })
          "
        >
          生成二维码
        </Button>
        <div class="grid grid-cols-2 gap-2">
          <div>
            <label class="mr-3">物资编码：</label>
            {{ materialObj.supplyCode }}
          </div>
          <div>
            <label class="mr-3">物资名称：</label>
            {{ materialObj.supplyName }}
          </div>
          <div>
            <label class="mr-3">物资类型：</label>
            {{ materialObj.typeId }}
          </div>
          <div>
            <label class="mr-3">规格型号：</label>
            {{ materialObj.specification }}
          </div>
          <div>
            <label class="mr-3">品牌：</label>
            {{ materialObj.brand }}
          </div>
          <div>
            <label class="mr-3">生产厂家：</label>
            {{ materialObj.manufacturer }}
          </div>
          <div>
            <label class="mr-3">单位：</label>
            {{ materialObj.unit }}
          </div>
          <div>
            <label class="mr-3">存放点：</label>
            {{
              storePointList.find(
                (item) =>
                  item.locationId ===
                  (materialObj as any)?.cmInventoryInfoLocationId,
              )?.locationName ?? ''
            }}
          </div>
          <div>
            <label class="mr-3">库存数量：</label>
            {{ (materialObj as any)?.cmInventoryInfoCurrentQuantity }}
          </div>
          <div>
            <label class="mr-3">安全库存：</label>
            {{ (materialObj as any)?.cmInventoryInfoSafetyQuantity }}
          </div>
          <div>
            <label class="mr-3">入库日期：</label>
            {{
              dayjs((materialObj as any)?.cmInventoryInfoInDate).format(
                'YYYY-MM-DD',
              )
            }}
          </div>
          <div>
            <label class="mr-3">有效期至：</label>
            {{
              dayjs((materialObj as any)?.cmInventoryInfoExpiryDate).format(
                'YYYY-MM-DD',
              )
            }}
          </div>
          <div>
            <label class="mr-3">状态：</label>
            {{
              EmergencyMaterialStatusMap[
                (materialObj as any)?.cmInventoryInfoStatus
              ] ?? ''
            }}
          </div>
        </div>
        <div class="pt-5">
          <div class="mr-3 font-bold">备注：</div>
          <div class="m-2">
            {{ materialObj.remarks }}
          </div>
        </div>
      </Card>
      <!-- <Card title="备注" class="mt-4" size="small">
        {{ materialObj.remarks }}
      </Card> -->
      <Card title="图片" class="mt-4" size="small">
        <ImagePreviewGroup>
          <div class="flex flex-wrap gap-2 overflow-hidden">
            <div
              class="h-[200px] w-[200px] overflow-hidden rounded-sm border border-gray-300"
              v-for="item in materialObj.cmSupplyImageList"
              :key="item.imageId ?? item.imagePath"
            >
              <Image
                :width="200"
                :height="200"
                :src="item.imagePath"
                class="h-full w-full object-cover"
              />
            </div>
          </div>
        </ImagePreviewGroup>
      </Card>
    </div>
    <QRcode ref="QRcodeRef" title="物资二维码" />
  </Modal>
</template>
<style lang="css" scoped>
label {
  color: oklch(44.6% 0.043 257.281deg);
}
</style>
