<!--
 * @Author: Strayer
 * @Date: 2025-08-01
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-04
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\components\statistic-card.vue
-->
<script setup lang="ts">
import type { Component } from 'vue';

import { IconifyIcon } from '@vben/icons';

export type StatisticItem = {
  color?: string; // 卡片主颜色
  desc?: string; // 底部描述
  meta?: any;
  svgComponent?: Component; // 右下角svg组件
  svgName?: string; // 右下角svg名称：基于iconify
  tag?: string; // 右上角标签文本
  title: string; // 标题
  unit?: string; // 单位
  value: number | string; // 值
};

const props = defineProps<{
  total: Array<StatisticItem>;
}>();
</script>

<template>
  <div class="flex flex-wrap justify-between gap-4 pb-4">
    <div
      v-for="item in props.total"
      :key="item.title"
      class="flex-auto rounded-lg border-l-4 bg-white p-4 shadow-sm dark:bg-gray-800"
      :style="{
        borderColor: item.color ?? '#3bce87',
      }"
    >
      <div class="flex justify-between">
        <div>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ item.title }}
          </p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ item.value }}{{ item.unit ? ` ${item.unit}` : '' }}
          </p>
        </div>
        <div
          class="clear-both"
          :style="{
            color: item.color ?? '#3bce87',
          }"
        >
          <div v-if="item.tag" class="mb-3">
            {{ item.tag }}
          </div>
          <component
            class="float-right h-6 w-6"
            v-if="item.svgComponent"
            :is="item.svgComponent"
            :style="{
              fill: item.color ?? '#3bce87',
            }"
          />
          <IconifyIcon
            v-if="item.svgName"
            :icon="item.svgName"
            class="h-6 w-6"
          />
        </div>
      </div>
      <slot :item="item" name="desc">
        <p
          v-if="item.desc"
          class="mt-4 text-sm text-gray-600 dark:text-gray-400"
        >
          {{ item.desc }}
        </p>
      </slot>
    </div>
  </div>
</template>
