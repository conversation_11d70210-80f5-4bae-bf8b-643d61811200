/*
 * @Author: Strayer
 * @Date: 2025-08-15
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-15
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\api\core\energy-management\alarm-record.ts
 */

import { requestClientGridPage } from '#/api/request';

/**
 * 告警记录
 *
 * WarningReportDTO
 */
export type WarningReportDTO = {
  /**
   * 实际值
   */
  actualValue?: number;
  /**
   * 区域ID
   */
  areaId?: string;
  /**
   * 区域名称
   */
  areaName?: string;
  /**
   * 设备ID
   */
  deviceId?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 偏差率
   */
  ratio?: number;
  /**
   * 告警报告唯一标识符
   */
  reportId: string;
  /**
   * 告警时间
   */
  reportTime?: string;
  /**
   * 类型（1:水，2:电，3:标煤，4:碳排放）
   */
  type?: number;
  /**
   * 标准值
   */
  value?: number;
};

/** 获取告警记录列表 type: 4:碳排放，NULL：其他3种*/
export async function getAlarmRecordListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: WarningReportDTO[];
    total: number;
  }>('/carbon/warningReport/page', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}
