import type { WarningReportDTO } from '#/api/core/energy-management/alarm-record';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-15
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\energy-management\alarm-record\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { UsageTypeMap } from '../carbon-emission-alarm-config/data';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    // {
    //   fieldName: 'name',
    //   label: '专家名称',
    // },
    // {
    //   fieldName: 'type',
    //   label: '擅长处理类型',
    // },
    // {
    //   fieldName: 'level',
    //   label: '专家级别',
    // },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<WarningReportDTO> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    // 告警时间
    {
      field: 'reportTime',
      title: '告警时间',
      customType: 'date',
    },
    // 用能区域
    {
      field: 'areaName',
      title: '用能区域',
    },
    // 用能设备
    {
      field: 'deviceName',
      title: '用能设备',
    },
    // 用能类型
    {
      field: 'type',
      title: '用能类型',
      formatter: ({ cellValue }: { cellValue: number }) => {
        return UsageTypeMap[cellValue]?.label ?? cellValue ?? '';
      },
    },
    // 标准值
    {
      field: 'value',
      title: '标准值',
    },
    // 实际值
    {
      field: 'actualValue',
      title: '实际值',
    },
    // 偏差率
    {
      field: 'ratio',
      title: '偏差率',
      formatter: ({ cellValue }: { cellValue: number }) => {
        if (cellValue > 0) {
          return `+${cellValue}%`;
        } else if (cellValue < 0) {
          return `-${cellValue}%`;
        }

        return `${cellValue ?? '--'}%`;
      },
    },
  ];
}
