/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-31
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-01
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\router\routes\modules\emergency-drill.ts
 */
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'svg:emergency-drill',
      title: '应急演练',
    },
    name: 'emergency-drill',
    path: '/emergency-drill',
    redirect: '/emergency-drill/drill-plan',
    children: [
      {
        meta: {
          title: '演练计划管理',
        },
        name: 'EmergencyDrillDrillPlan',
        path: '/emergency-drill/drill-plan',
        component: () => import('#/views/emergency-drill/drill-plan/index.vue'),
      },
      {
        meta: {
          title: '演练记录管理',
        },
        name: 'EmergencyDrillDrillRecord',
        path: '/emergency-drill/drill-record',
        component: () =>
          import('#/views/emergency-drill/drill-record/index.vue'),
      },
      {
        meta: {
          title: '演练评估管理',
        },
        name: 'EmergencyDrillDrillEvaluate',
        path: '/emergency-drill/drill-evaluate',
        component: () =>
          import('#/views/emergency-drill/drill-evaluate/index.vue'),
      },
    ],
  },
];

export default routes;
