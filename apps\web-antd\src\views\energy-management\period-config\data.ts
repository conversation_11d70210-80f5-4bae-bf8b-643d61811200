import type {
  EnPeriodConfig,
  EnPeriodType,
} from '#/api/core/energy-management/period-config';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import dayjs from 'dayjs';

// 表格列头
export function getColumnsScheme(
  periodTypes: EnPeriodType[],
): CustomColumnSchema<EnPeriodConfig> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'periodId', title: 'ID', visible: false },
    // 时段类型ID
    {
      field: 'periodName',
      title: '时段类型',
      customType: 'tag',
      formatter: ((params: any) => {
        const periodType = periodTypes.find(
          (item) => item.periodTypeId === params.row.periodTypeId,
        );
        return [
          {
            label: params.row.periodName,
            color: periodType?.periodColor,
          },
        ];
      }) as any,
    },
    // 开始时间 格式：HH:mm
    {
      field: 'startTime',
      title: '开始时间',
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? dayjs(cellValue).format('HH:mm') : '';
      },
    },
    // 结束时间 格式：HH:mm
    {
      field: 'endTime',
      title: '结束时间',
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? dayjs(cellValue).format('HH:mm') : '';
      },
    },
    // 适用范围
    { field: 'applyRange', title: '适用范围' },
    // 状态
    {
      field: 'status',
      title: '状态',
      formatter: ({ cellValue }: { cellValue: number }) => {
        return cellValue === 1 ? '启用' : '禁用';
      },
    },
    // 创建时间 默认隐藏 时间类型
    {
      field: 'createTime',
      title: '创建时间',
      customType: 'date',
      visible: false,
    },
    // 更新时间 默认隐藏
    {
      field: 'updateTime',
      title: '更新时间',
      customType: 'date',
      visible: false,
    },
    {
      field: 'action',
      title: '操作',
      width: 180,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(
  periodTypeList: EnPeriodType[],
): CustomFormSchema[] {
  return [
    {
      fieldName: 'periodTypeId',
      label: '时段类型',
      rules: 'required',
      component: 'Select',
      componentProps: {
        placeholder: '请选择时段类型',
        options: periodTypeList.map((item) => ({
          label: item.periodName,
          value: item.periodTypeId,
        })),
      },
    },
    {
      fieldName: 'startTime',
      label: '开始时间',
      rules: 'required',
      component: 'TimePicker',
      componentProps: {
        placeholder: '请选择开始时间',
        format: 'HH:mm',
      },
    },
    {
      fieldName: 'endTime',
      label: '结束时间',
      rules: 'required',
      component: 'TimePicker',
      componentProps: {
        placeholder: '请选择结束时间',
        format: 'HH:mm',
      },
    },
    {
      fieldName: 'applyRange',
      label: '适用范围',
      rules: 'required',
      component: 'Input',
      componentProps: {
        placeholder: '请输入适用范围，如：周一至周五',
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      rules: 'required',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ];
}
