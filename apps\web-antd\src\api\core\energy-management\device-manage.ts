import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * EnDevice对象
 *
 * EnDevice
 */
export type EnDevice = {
  /**
   * 所属区域ID
   */
  areaId: string;
  /**
   * 创建时间戳（创建时自动设置）
   */
  createTime?: string;
  /**
   * 设备编号
   */
  deviceCode?: string;
  /**
   * 设备唯一标识符
   */
  deviceId: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 设备状态：1=正常，2=需维护，3=异常，4=离线
   */
  status?: number;
  /**
   * 更新时间戳（更新时自动设置）
   */
  updateTime?: string;
};

/** 获取设备列表 */
export async function getDeviceListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: EnDevice[];
    total: number;
  }>('/energyConsumptionManagement/device/page', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 新增设备 */
export async function addDeviceApi(data: EnDevice) {
  return requestClient.post<EnDevice>(
    '/energyConsumptionManagement/device',
    data,
  );
}

/** 修改设备 */
export async function editDeviceApi(data: EnDevice) {
  return requestClient.put<EnDevice>(
    '/energyConsumptionManagement/device',
    data,
  );
}

/** 删除设备 */
export async function deleteDeviceApi(deviceId: string) {
  return requestClient.delete<string>(
    `/energyConsumptionManagement/device/${deviceId}`,
  );
}
