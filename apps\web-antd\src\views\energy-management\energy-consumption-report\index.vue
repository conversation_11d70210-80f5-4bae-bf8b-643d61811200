<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { EnArea } from '#/api/core/energy-management/area-manage';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, onMounted, ref, shallowRef, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { listAreaApi } from '#/api/core/energy-management/area-manage';
// API 方法
import {
  addMeasurementReportApi,
  deleteMeasurementReportApi,
  editMeasurementReportApi,
  getMeasurementReportDefaultApi,
  getMeasurementReportListApi,
} from '#/api/core/energy-management/energy-consumption-report';
import FormModal from '#/components/form-modal/index.vue';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import {
  getColumnsScheme,
  getFormOption,
  getTopSearchScheme,
  reportDataMap,
} from './data';

const router = useRouter();

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');
const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<any>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef([]);

const areaList = shallowRef<EnArea[]>([]);
listAreaApi().then((res) => {
  areaList.value = res;
  formOption.value = getFormOption(res);
});

const eidtMode = ref<'create' | 'edit'>();
const editRowData = ref<any>({});

const createReportDate = ref();
const beforeCreateFormModalRef = useTemplateRef('beforeCreateFormModalRef');
const beforeCreateFormOption = shallowRef<CustomFormSchema[]>([
  {
    fieldName: 'type',
    label: '月/日',
    rules: 'required',
    labelWidth: 60,
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '月', value: 1 },
        { label: '日', value: 2 },
      ],
    },
  },
  {
    fieldName: 'date',
    label: '日期',
    rules: 'required',
    labelWidth: 60,
    component: 'DatePicker',
    componentProps: {
      type: 'date',
    },
  },
]);
async function beforeCreateSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}) {
  const res = await getMeasurementReportDefaultApi({
    date: param.formData.date,
    type: param.formData.type,
  });

  createReportDate.value = param.formData.date;

  const newRowData: any = {};
  Object.keys(res).forEach((key) => {
    if (key === 'factorPriceDetail') {
      Object.keys(res.factorPriceDetail).forEach((key) => {
        newRowData[`detail_${key}`] = res.factorPriceDetail[key];
      });
    } else {
      newRowData[key] = res[key];
    }
  });

  eidtMode.value = 'create';
  gridFormRef.value?.formModalRef?.open({
    title: '新增能耗报表',
    formData: newRowData,
    meta: { type: eidtMode.value },
  });
  return true;
}

// 查询表格数据
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<any>,
  fromData: any,
) {
  const res = await getMeasurementReportListApi(param.page, fromData);
  return {
    items: res.data,
    total: res.total,
  };
}

// 删除
function deleteRow(row: any) {
  return deleteMeasurementReportApi(row.reportId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

// 编辑
function editRow(row: any) {
  eidtMode.value = 'edit';
  editRowData.value = { ...row };

  const {
    waterReportDetail,
    electricityReportDetail,
    factorPriceDetail,
    _X_ROW_KEY,
    ...rest
  } = row;

  const newRowData = { ...rest };

  if (waterReportDetail) {
    newRowData.waterReportDetailList = JSON.parse(waterReportDetail);
  }
  if (electricityReportDetail) {
    newRowData.electricityReportDetailList = JSON.parse(
      electricityReportDetail,
    );
  }
  if (factorPriceDetail) {
    const obj = JSON.parse(factorPriceDetail);
    Object.keys(obj).forEach((key) => {
      newRowData[`detail_${key}`] = obj[key];
    });
  }

  gridFormRef.value?.formModalRef?.open({
    title: '编辑能耗报表',
    formData: newRowData,
    meta: { type: eidtMode.value, raw: newRowData },
  });
}

function handleCreate() {
  beforeCreateFormModalRef.value?.open({
    title: '选择计量数据',
    formData: {
      type: 2,
    },
  });
}

function handleData(data: Record<string, any[] | string>) {
  const res: any = {};
  const factorPriceDetail: any = {};

  if (eidtMode.value === 'create') {
    res.reportDate = createReportDate.value;
  } else if (eidtMode.value === 'edit') {
    res.reportId = editRowData.value.reportId;
  }

  Object.keys(data).forEach((key) => {
    if (key === 'waterReportDetailList') {
      const newData = (data[key] as any[]).map((item: any) => {
        const area = areaList.value.find((area) => area.areaId === item.areaId);
        return {
          ...item,
          areaName: area?.areaName,
        };
      });

      res.waterReportDetail = data[key] ? JSON.stringify(newData) : null;
    } else if (key === 'electricityReportDetailList') {
      const newData = (data[key] as any[]).map((item: any) => {
        const area = areaList.value.find((area) => area.areaId === item.areaId);
        return {
          ...item,
          areaName: area?.areaName,
        };
      });

      res.electricityReportDetail = data[key] ? JSON.stringify(newData) : null;
    } else if (key.startsWith('detail_')) {
      factorPriceDetail[key.replace('detail_', '')] = data[key];
    } else {
      res[key] = data[key];
    }
  });

  res.factorPriceDetail = JSON.stringify(factorPriceDetail);

  return res;
}

// 新建/编辑提交
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  if (param.meta.type === 'create')
    return addMeasurementReportApi(handleData(param.formData))
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.formApi.submitForm();
        createReportDate.value = undefined;
        editRowData.value = {};
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    return editMeasurementReportApi(handleData(param.formData))
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.formApi.submitForm();
        createReportDate.value = undefined;
        editRowData.value = {};
        return true;
      })
      .catch(() => false);
  return Promise.resolve(true);
}

function viewRow(row: any) {
  reportDataMap.value[row.reportId] = row;
  router.push({
    name: 'EnergyMonitorEnergyConsumptionReportDetail',
    query: {
      reportId: row.reportId,
    },
  });
}

onMounted(() => {
  gridFormRef.value?.gridApi.formApi.setValues({
    date: dayjs().startOf('month'),
  });
});
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="能耗报表"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
        modal-class="w-[1000px]"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="handleCreate"
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="viewRow(row)"> 查看详情 </Button>
          <Button type="link" @click="editRow(row)"> 编辑 </Button>
          <RemoveButton @confirm="deleteRow(row)" />
        </template>
      </GridForm>
    </template>
    <FormModal
      ref="beforeCreateFormModalRef"
      modal-class="w-[400px]"
      :form-option="beforeCreateFormOption"
      :submit="beforeCreateSubmit"
    />
  </Page>
</template>

<style scoped lang="scss"></style>
