import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * 时段类型
 * EnPeriodType
 */
export type CarbonEmissionPlan = {
  actualEmission: number;
  createdTime: string;
  detailList: CarbonEmissionPlanDetail[];
  planId: number;
  targetEmission: number;
  updatedTime: string;
  year: number;
};

export type CarbonEmissionPlanDetail = {
  actualEmission: number;
  createdTime: string;
  detailId: number;
  month: number;
  planId: number;
  plannedEmission: number;
  updatedTime: string;
};

/**
 * @returns 碳排放计划列表
 */
export async function getCarbonEmissionPlanListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: CarbonEmissionPlan[];
    total: number;
  }>('/carbon/plan/page', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/**
 * 新增碳排放计划
 */
export async function addCarbonEmissionPlanApi(data: CarbonEmissionPlan) {
  return requestClient.post<CarbonEmissionPlan>('/carbon/plan', data);
}

/**
 * 修改碳排放计划
 */
export async function editCarbonEmissionPlanApi(data: CarbonEmissionPlan) {
  return requestClient.put<CarbonEmissionPlan>('/carbon/plan', data);
}

/**
 * 删除碳排放计划
 */
export async function deleteCarbonEmissionPlanApi(planId: number) {
  return requestClient.delete<string>(`/carbon/plan/${planId}`);
}

/**
 * 计算碳排放计划
 */
export async function calculateCarbonEmissionPlanApi(planId: number) {
  return requestClient.post<string>(`/carbon/planCalculate?planId=${planId}`);
}
