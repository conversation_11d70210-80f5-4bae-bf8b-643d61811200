import type { FfMonitoringAlarm } from '#/api/core/fire-management/monitor-alarm';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-25
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\fire-management\monitor-alarm\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'deviceId',
      label: '设备ID',
    },
    {
      fieldName: 'deviceName',
      label: '设备名称',
    },
    {
      fieldName: 'deviceType',
      label: '设备类型',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<FfMonitoringAlarm> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'deviceId', title: '设备ID' },
    { field: 'deviceName', title: '设备名称' },
    { field: 'deviceType', title: '设备类型' },
    {
      title: '责任人',
      field: 'responsiblePerson',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'deviceName',
      label: '设备名称',
      rules: 'required',
    },
    {
      fieldName: 'deviceType',
      label: '设备类型',
      rules: 'required',
    },
    {
      fieldName: 'installDate',
      label: '安装日期',
      component: 'DatePicker',
      rules: 'required',
    },
    {
      fieldName: 'installLocation',
      label: '安装位置',
      rules: 'required',
    },
    {
      fieldName: 'responsiblePerson',
      label: '维护责任人',
      rules: 'required',
    },
  ];
}
