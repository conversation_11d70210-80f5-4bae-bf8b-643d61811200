<script lang="ts" setup>
import type { Ref, ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CmAccidentBasicInfo } from '#/api/core/emergency/evaluation';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';
import type { StatisticItem } from '#/components/statistic-card.vue';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';
import { SvgICircleIcon } from '@vben/icons';

import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { Button, message, Popconfirm } from 'ant-design-vue';
import * as echarts from 'echarts';

import {
  addEmergencyEvaluationApi,
  deleteEmergencyEvaluationApi,
  editEmergencyEvaluationApi,
  getEmergencyEvaluationCountApi,
  getEmergencyEvaluationDetailApi,
  getEmergencyEvaluationListApi,
} from '#/api/core/emergency/evaluation';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';
import StatisticCard from '#/components/statistic-card.vue';
import { downloadFile } from '#/utils/comm';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<CmAccidentBasicInfo>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<CmAccidentBasicInfo>,
  fromData: any,
) {
  if (fromData.dateRange) {
    fromData.beginDate = fromData.dateRange[0].startOf('day').toISOString();
    fromData.endDate = fromData.dateRange[1].endOf('day').toISOString();
    delete fromData.dateRange;
  }

  getCountData();

  return getEmergencyEvaluationListApi(param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: CmAccidentBasicInfo) {
  return deleteEmergencyEvaluationApi([row.accidentId]).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/** 批量删除 */
function deleteMultiple() {
  const records = gridFormRef.value?.gridApi.grid.getCheckboxRecords();
  const ids = records?.map((item) => item.accidentId) as string[];
  return deleteEmergencyEvaluationApi(ids).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: CmAccidentBasicInfo) {
  gridFormRef.value?.gridApi.setLoading(true);
  getEmergencyEvaluationDetailApi(row.accidentId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '事故报告',
        formData: {
          ...res,

          cmAccidentReportsAccidentProcess:
            res.cmAccidentReports?.accidentProcess,
          cmAccidentReportsSiteSurvey: res.cmAccidentReports?.siteSurvey,
          cmAccidentReportsCauseAnalysis: res.cmAccidentReports?.causeAnalysis,
          cmAccidentReportsResponsibility:
            res.cmAccidentReports?.responsibility,
          cmAccidentReportsRectification: res.cmAccidentReports?.rectification,

          cmAccidentAttachmentsList: res.cmAccidentAttachmentsList?.map(
            (item) => ({
              uid: item.attachmentId,
              name:
                item.attachmentName ??
                item.storagePath?.slice(
                  Math.max(0, item.storagePath.lastIndexOf('/') + 1),
                ) ??
                'file',
              status: 'done',
              url: item.storagePath,
            }),
          ),
        },
        meta: { type: 'edit', raw: row },
      });
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: CmAccidentBasicInfo;
}): Promise<boolean> {
  param.formData.cmAccidentReports = {
    accidentId: param.meta.raw?.accidentId,
    accidentProcess: param.formData.cmAccidentReportsAccidentProcess,
    siteSurvey: param.formData.cmAccidentReportsSiteSurvey,
    causeAnalysis: param.formData.cmAccidentReportsCauseAnalysis,
    responsibility: param.formData.cmAccidentReportsResponsibility,
    rectification: param.formData.cmAccidentReportsRectification,
  };

  Reflect.deleteProperty(param.formData, 'cmAccidentReportsAccidentProcess');
  Reflect.deleteProperty(param.formData, 'cmAccidentReportsSiteSurvey');
  Reflect.deleteProperty(param.formData, 'cmAccidentReportsCauseAnalysis');
  Reflect.deleteProperty(param.formData, 'cmAccidentReportsResponsibility');
  Reflect.deleteProperty(param.formData, 'cmAccidentReportsRectification');

  param.formData.cmAccidentAttachmentsList =
    param.formData.cmAccidentAttachmentsList?.map((item: any) => ({
      accidentId: param.meta.raw?.accidentId,
      attachmentId: item.uid,
      attachmentName: item.name,
      storagePath: item.url,
    }));

  if (param.meta.type === 'create')
    addEmergencyEvaluationApi(param.formData)
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editEmergencyEvaluationApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}
const defaultColor = ref('rgb(239, 189, 72)');

// 统计
const totalConfigMap: Ref<Record<string, { color: string }>> = ref({
  事件总数: {
    color: 'rgb(59, 130, 246)',
  },
  事故灾难: {
    color: 'rgb(96, 165, 250)',
  },
  自然灾害: {
    color: 'rgb(102, 194, 165)',
  },
  公共卫生事件: {
    color: 'rgb(239, 189, 72)',
  },
  其他灾难: {
    color: 'rgb(168, 85, 247)',
  },
});
const total = ref<StatisticItem[]>([]);

/** 获取统计数据 */
function getCountData() {
  getEmergencyEvaluationCountApi().then((res) => {
    total.value = [
      {
        title: '事件总数',
        value: res.totalCount,
        svgName: 'eva:list-fill',
        color: totalConfigMap.value['事件总数']?.color ?? defaultColor.value,
      },
    ];

    for (const key of Object.keys(res.typeMap ?? {})) {
      total.value.push({
        title: key,
        value: res.typeMap[key] ?? 0,
        svgComponent: SvgICircleIcon,
        color: totalConfigMap.value[key]?.color ?? defaultColor.value,
      });
    }

    renderEcharts(res.typeDateMap);
  });
}

let myChart: any;
/** 渲染图表 */
function renderEcharts(typeDateMap: Record<string, Record<string, number>>) {
  if (!myChart)
    myChart = echarts.init(
      document.querySelector('#emergency-evaluation-echarts') as HTMLElement,
    );

  const xAxisData = Object.keys(typeDateMap);
  const seriesDataMap: Record<string, (number | undefined)[]> = {};

  for (const item of Object.values(typeDateMap)) {
    for (const key of Object.keys(item)) {
      if (!seriesDataMap[key]) seriesDataMap[key] = [];
      seriesDataMap[key].push(item[key]);
    }
  }

  // 绘制图表
  myChart.setOption({
    title: {
      text: '事故类型分布',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
      },
      top: 4,
      let: 22,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: Object.keys(seriesDataMap),
    },
    xAxis: {
      data: xAxisData,
    },
    yAxis: {},
    series: Object.keys(seriesDataMap).map((item) => ({
      name: item,
      type: 'line',
      data: seriesDataMap[item],
    })),
    // series: [
    //   {
    //     name: '销量',
    //     type: 'line',
    //     data: [5, 20, 36, 10, 10, 20],
    //   },
    // ],
  });
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <!-- 统计 -->
      <StatisticCard :total="total" />
      <!-- 事故类型分别 -->
      <div class="pd-4">
        <div id="emergency-evaluation-echarts" class="h-[300px] bg-white"></div>
      </div>
      <!-- 表格 -->
      <GridForm
        ref="gridFormRef"
        title="事故报告列表"
        style="height: calc(100vh - 500px)"
        :grid-options="{
          checkboxConfig: {},
        }"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            class="mr-2"
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增事故报告',
                  meta: { type: 'create' },
                });
              }
            "
          />
          <Popconfirm title="是否确认删除?" @confirm="deleteMultiple()">
            <Button
              type="primary"
              danger
              shape="circle"
              :icon="h(DeleteOutlined)"
            />
          </Popconfirm>
        </template>
        <template #action="{ row }: { row: CmAccidentBasicInfo }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <!-- 下载 -->
          <Button
            type="link"
            @click="
              row.cmAccidentAttachmentsList?.forEach((item) => {
                downloadFile(item.storagePath ?? '', item.attachmentName ?? '');
              })
            "
          >
            下载
          </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
