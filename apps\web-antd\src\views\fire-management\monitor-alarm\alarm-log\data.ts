/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-25
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-25
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\fire-management\monitor-alarm\alarm-log\data.ts
 */
import type { FfEquipmentAlarmRecord } from '#/api/core/fire-management/monitor-alarm';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'alarmType',
      label: '告警类型',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<FfEquipmentAlarmRecord> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'alarmId', title: '告警ID', visible: false },
    {
      title: '告警时间',
      field: 'alarmTime',
      customType: 'dateDay',
    },
    {
      title: '告警类型',
      field: 'alarmType',
    },
    {
      title: '告警详情',
      field: 'alarmDetail',
    },
    {
      title: '处理状态',
      field: 'processStatus',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'alarmType',
      label: '告警类型',
      rules: 'required',
    },
    {
      fieldName: 'alarmTime',
      label: '告警时间',
      rules: 'required',
      component: 'DatePicker',
    },
    {
      fieldName: 'alarmDetail',
      label: '告警详情',
      rules: 'required',
      component: 'Textarea',
    },
    {
      fieldName: 'processStatus',
      label: '处理状态',
      rules: 'required',
    },
  ];
}
