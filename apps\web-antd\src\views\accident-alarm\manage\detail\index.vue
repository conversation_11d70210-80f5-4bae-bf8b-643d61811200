<script lang="ts" setup>
import type { ComponentExposed } from 'vue-component-type-helpers';

import type {
  CmWarningHistory,
  CmWarningRegister,
} from '#/api/core/accident-alarm/manage';

import { h, onMounted, ref, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  ArrowLeftOutlined,
  EditOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';
import {
  Button,
  Card,
  Col,
  message,
  Row,
  Select,
  SelectOption,
  Tag,
  Textarea,
  Timeline,
  TimelineItem,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  getAccidentAlarmDetail,
  updateAccidentAlarm,
} from '#/api/core/accident-alarm/manage';
import { PriorityMap } from '#/data-model/comm';

import { LevelMap } from '../data';
import CommandDetail from './command-detail.vue';

const props = defineProps({
  parentId: {
    type: String,
    default: '',
  },
});

const router = useRouter();

const commandDetailRef =
  useTemplateRef<ComponentExposed<typeof CommandDetail>>('commandDetailRef');

const manageObj = ref<CmWarningRegister>();

onMounted(async () => {
  if (props.parentId) {
    manageObj.value = await getAccidentAlarmDetail(props.parentId);
  } else {
    message.error('路由参数错误');
  }
});

/** 状态更新 */
const handleStatusChange = async () => {
  if (manageObj.value) {
    await updateAccidentAlarm(manageObj.value);
    message.success('更新成功');
  }
};

/** 新增指令 */
const addCommand = () => {
  if (!manageObj.value?.warningHistoryList)
    manageObj.value!.warningHistoryList = [];

  commandDetailRef.value?.open(manageObj.value!);
};

/** 编辑指令 */
const editCommand = (record: CmWarningHistory) => {
  commandDetailRef.value?.open(manageObj.value!, record);
};

/** 指令已更新 */
const handleEdit = async () => {
  manageObj.value = await getAccidentAlarmDetail(props.parentId);
};
</script>

<template>
  <Page auto-content-height>
    <template v-if="manageObj">
      <div>
        <Row :gutter="[24, 24]">
          <Col :span="24" :lg="8">
            <Card :bordered="false">
              <template #title>
                <div
                  class="cursor-pointer text-base"
                  @click="router.push('/accident-alarm/manage')"
                >
                  <ArrowLeftOutlined /><span class="ml-2">事件基本信息</span>
                </div>
              </template>
              <Tag
                class="absolute right-3 top-3"
                :color="LevelMap[manageObj?.warningLevel ?? '']?.color"
              >
                {{ manageObj?.warningLevel }}
              </Tag>
              <div class="border-[hsl(240 5.9% 90%)] flex justify-between pb-3">
                <div class="label">事件编号</div>
                <div>{{ manageObj?.warningId }}</div>
              </div>
              <div
                class="border-[hsl(240 5.9% 90%)] flex justify-between border-t-[1px] pb-3 pt-7"
              >
                <div class="label">事件标题</div>
                <div>{{ manageObj?.warningTitle }}</div>
              </div>
              <div
                class="border-[hsl(240 5.9% 90%)] flex justify-between border-t-[1px] pb-3 pt-7"
              >
                <div class="label">发生时间</div>
                <div>
                  {{
                    manageObj?.occurrenceTime
                      ? dayjs(manageObj?.occurrenceTime).format(
                          'YYYY-MM-DD HH:mm:ss',
                        )
                      : ''
                  }}
                </div>
              </div>
              <div
                class="border-[hsl(240 5.9% 90%)] flex justify-between border-t-[1px] pb-3 pt-7"
              >
                <div class="label">事件类型</div>
                <div>{{ manageObj?.warningType }}</div>
              </div>
              <!-- <div
                class="border-[hsl(240 5.9% 90%)] flex justify-between border-t-[1px] pb-3 pt-7"
              >
                <div class="label">事件等级</div>
                <div>{{ manageObj?.warningLevel }}</div>
              </div> -->
              <div
                class="border-[hsl(240 5.9% 90%)] flex justify-between border-t-[1px] pb-3 pt-7"
              >
                <div class="label">责任部门</div>
                <div>{{ manageObj?.responsibleDepartment }}</div>
              </div>
              <div
                class="border-[hsl(240 5.9% 90%)] flex justify-between border-t-[1px] pb-3 pt-7"
              >
                <div class="label">当前状态</div>
                <div>
                  <Select
                    v-model:value="manageObj.processingStatus"
                    style="width: 120px"
                    @change="handleStatusChange"
                  >
                    <SelectOption value="处理中">处理中</SelectOption>
                    <SelectOption value="已处理">已处理</SelectOption>
                  </Select>
                </div>
              </div>
              <div
                class="border-[hsl(240 5.9% 90%)] flex justify-between border-t-[1px] pb-3 pt-7"
              >
                <div class="label">事件上报人</div>
                <div>{{ manageObj?.reporterName }}</div>
              </div>
              <div class="border-[hsl(240 5.9% 90%)] border-t-[1px] pt-7">
                <div class="label font-bold">事件概述</div>
                <div class="mt-3">{{ manageObj?.doverview }}</div>
              </div>
            </Card>
            <Card class="mt-6" title="事件进展时间线">
              <Timeline>
                <TimelineItem
                  v-for="item in manageObj?.warningHistoryList"
                  :key="item.recordId"
                  :color="PriorityMap[item.level ?? '']?.color"
                >
                  <div class="flex justify-between">
                    <div class="label text-base">{{ item.title }}</div>
                    <div>
                      {{
                        item.createTime
                          ? dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
                          : ''
                      }}
                    </div>
                  </div>
                  <div class="mt-3 text-sm">{{ item.overview }}</div>
                </TimelineItem>
              </Timeline>
            </Card>
          </Col>
          <Col :span="24" :lg="16">
            <Card title="指令列表">
              <div class="absolute right-6 top-3">
                <Button
                  type="primary"
                  shape="circle"
                  :icon="h(PlusOutlined)"
                  @click="addCommand"
                />
              </div>
              <div>
                <Card
                  v-for="item in manageObj?.warningHistoryList"
                  :key="item.recordId"
                  class="mt-6 first:mt-0"
                  size="small"
                >
                  <div class="flex justify-between">
                    <div class="label text-lg">{{ item.title }}</div>
                    <div>
                      <Tag :color="PriorityMap[item.level ?? '']?.color">
                        {{ item.level }}
                      </Tag>
                    </div>
                  </div>
                  <div class="mt-2 text-base">{{ item.overview }}</div>
                  <Textarea
                    class="mt-3 bg-[#f8fafc]"
                    :value="item.detail"
                    readonly
                    autosize
                  />
                  <div class="mt-3 flex justify-between text-sm">
                    <div>
                      发布时间：{{
                        item.createTime
                          ? dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
                          : ''
                      }}
                    </div>
                    <div>
                      <Button
                        type="link"
                        :icon="h(EditOutlined)"
                        @click="editCommand(item)"
                      />
                    </div>
                  </div>
                </Card>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </template>
    <CommandDetail ref="commandDetailRef" @had-edit="handleEdit" />
  </Page>
</template>
<style lang="css" scoped>
.label {
  color: oklch(44.6% 0.043 257.281deg);
}
</style>
