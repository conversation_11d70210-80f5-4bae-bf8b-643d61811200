<script lang="ts" setup>
import type { ShallowRef } from 'vue';
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CmDrillBasicInfo } from '#/api/core/emergency-drill/drill-evaluate';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';
import type { StatisticItem } from '#/components/statistic-card.vue';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';
import {
  SvgCheckCircleIcon,
  SvgFileIcon,
  SvgStarIcon,
  SvgWaitingIcon,
} from '@vben/icons';

import {
  FallOutlined,
  PlusOutlined,
  RiseOutlined,
} from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';
import * as echarts from 'echarts';

import {
  deleteDrillEvaluateApi,
  editDrillEvaluateApi,
  getDrillEvaluateCountApi,
  getDrillEvaluateDetailApi,
  getDrillEvaluateListApi,
} from '#/api/core/emergency-drill/drill-evaluate';
import FormModal from '#/components/form-modal/index.vue';
import GridForm from '#/components/grid-form/index.vue';
// import RemoveButton from '#/components/remove-button.vue';
import StatisticCard from '#/components/statistic-card.vue';

import {
  getColumnsScheme,
  getFormOption,
  getFormOptionRecord,
  getTopSearchScheme,
} from './data';
import Detail from './detail.vue';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');
const formModalRef =
  useTemplateRef<ComponentExposed<typeof FormModal>>('formModalRef');
const detailRef = useTemplateRef<ComponentExposed<typeof Detail>>('detailRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<CmDrillBasicInfo>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<CmDrillBasicInfo>,
  fromData: any,
) {
  // 时间范围
  if (fromData.timeRange) {
    fromData.beginDate = fromData.timeRange[0].startOf('day').toISOString();
    fromData.endDate = fromData.timeRange[1].endOf('day').toISOString();
    delete fromData.timeRange;
  }

  getCountData();

  return getDrillEvaluateListApi(false, param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: CmDrillBasicInfo) {
  return deleteDrillEvaluateApi(row.drillId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}
console.log(deletePersonnel);

/**
 * @description: 编辑
 */
function editPersonnel(row: CmDrillBasicInfo) {
  gridFormRef.value?.gridApi.setLoading(true);
  getDrillEvaluateDetailApi(row.drillId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '编辑演练评估',
        formData: {
          ...res,
          rating: res.rating ? JSON.parse(res.rating) : [],
        },
        meta: { type: 'edit', raw: row },
      });
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: CmDrillBasicInfo;
}): Promise<boolean> {
  if (param.formData.rating) {
    param.formData.fraction = param.formData.rating.reduce(
      (acc: number, cur: any) => acc + cur.score,
      0,
    );
    param.formData.rating = JSON.stringify(param.formData.rating);
  }

  return editDrillEvaluateApi({
    ...param.raw,
    ...param.formData,
  })
    .then(() => {
      message.success('更新成功');
      if (param.meta.type === 'create') {
        gridFormRef.value?.gridApi.reload();
      } else {
        gridFormRef.value?.gridApi.query();
      }
      return true;
    })
    .catch(() => false);
}

// 绑定了演练记录
async function onSubmitRecord(param: { formData: any }) {
  const drillId = param.formData.drillId;

  gridFormRef.value?.gridApi.setLoading(true);
  return getDrillEvaluateDetailApi(drillId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '新建演练评估',
        formData: {
          ...res,
          rating: res.rating ? JSON.parse(res.rating) : [],
        },
        meta: { type: 'create', raw: res },
      });
      return true;
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

const total = ref<StatisticItem[]>([]);

function getCountData() {
  getDrillEvaluateCountApi().then((res) => {
    total.value = [
      {
        color: 'blue',
        title: '总评估数',
        value: res.totalCount ?? 0,
        svgComponent: SvgFileIcon,
        meta: {
          changePercentage: res.totalCountChangePercentage,
        },
      },
      {
        color: 'green',
        title: '评估通过率',
        value: `${(res.passRate ?? 0) * 100}%`,
        svgComponent: SvgCheckCircleIcon,
        meta: {
          changePercentage: res.passRateChangePercentage,
        },
      },
      {
        color: 'red',
        title: '待评估数',
        value: res.waitEvaluate ?? 0,
        svgComponent: SvgWaitingIcon,
        meta: {
          changePercentage: res.waitEvaluateChangePercentage,
        },
      },
      {
        color: 'purple',
        title: '平均得分',
        value: res.averageScore ?? 0,
        svgComponent: SvgStarIcon,
        meta: {
          changePercentage: res.averageScoreChangePercentage,
        },
      },
    ];

    renderEcharts(res.typeDateMap ?? {});
  });
}

let myChart: any;
/** 渲染图表 */
function renderEcharts(typeDateMap: Record<string, Record<string, number>>) {
  if (!myChart)
    myChart = echarts.init(
      document.querySelector(
        '#emergency-drill-evaluation-echarts',
      ) as HTMLElement,
    );

  const xAxisData = Object.keys(typeDateMap);
  const seriesDataMap: Record<string, (number | undefined)[]> = {};
  const legendData = new Set<string>();

  for (const item of Object.values(typeDateMap)) {
    for (const key of Object.keys(item)) {
      legendData.add(key);
    }
  }
  const legendDataArr = [...legendData];

  for (const item of Object.values(typeDateMap)) {
    for (const key of legendDataArr) {
      if (!seriesDataMap[key]) seriesDataMap[key] = [];
      seriesDataMap[key].push(item[key]);
    }
  }

  // 绘制图表
  myChart.setOption({
    title: {
      text: '评估结果分布',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
      },
      top: 4,
      let: 22,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: Object.keys(seriesDataMap),
    },
    xAxis: {
      data: xAxisData,
    },
    yAxis: {},
    series: Object.keys(seriesDataMap).map((item) => ({
      name: item,
      type: 'bar',
      data: seriesDataMap[item],
    })),
  });
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <StatisticCard :total="total">
        <template #desc="{ item }">
          <p>
            <RiseOutlined
              v-if="item.meta.changePercentage >= 0"
              class="text-green-500"
            />
            <FallOutlined
              v-if="item.meta.changePercentage < 0"
              class="text-red-500"
            />
            <span
              :class="{
                'text-green-500': item.meta.changePercentage >= 0,
                'text-red-500': item.meta.changePercentage < 0,
              }"
            >
              {{ item.meta.changePercentage }}%
            </span>
            <span>较上月</span>
          </p>
        </template>
      </StatisticCard>
      <!-- 图表 -->
      <div class="pb-4">
        <div
          id="emergency-drill-evaluation-echarts"
          class="h-[300px] bg-white"
        ></div>
      </div>
      <GridForm
        ref="gridFormRef"
        style="height: calc(100vh - 520px)"
        title="演练评估列表"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
        :form-config="{
          wrapperClass: 'grid-cols-2',
        }"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                formModalRef?.open({
                  title: '请选择要评估的演练记录',
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="detailRef?.open(row)"> 查看 </Button>
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <!-- <RemoveButton @confirm="deletePersonnel(row)" /> -->
        </template>
      </GridForm>
    </template>

    <!-- 选择关联的演练记录弹窗 -->
    <FormModal
      ref="formModalRef"
      :form-option="getFormOptionRecord()"
      :submit="onSubmitRecord"
    />
    <Detail ref="detailRef" />
  </Page>
</template>
