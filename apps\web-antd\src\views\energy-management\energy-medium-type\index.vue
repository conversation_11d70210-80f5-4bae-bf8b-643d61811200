<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { EnMediumType } from '#/api/core/energy-management/energy-medium-type';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, onMounted, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addEnergyMediumTypeApi,
  deleteEnergyMediumTypeApi,
  editEnergyMediumTypeApi,
  getEnergyMediumTypeListApi,
} from '#/api/core/energy-management/energy-medium-type';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<EnMediumType>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

let initSearch = false;

/**
 * @description: 获取表格数据
 */
async function query(
  _param: VxeGridPropTypes.ProxyAjaxQueryParams<EnMediumType>,
  fromData: any,
) {
  if (!initSearch) return;

  const res = await getEnergyMediumTypeListApi(fromData);
  return {
    items: res,
    total: res.length,
  };
}

onMounted(() => {
  initSearch = true;
  gridFormRef.value?.gridApi.formApi.setValues({
    status: 1,
  });
});

/**
 * @description: 删除
 */
function deleteMediumType(row: EnMediumType) {
  return deleteEnergyMediumTypeApi(row.mediumId!).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  return param.meta.type === 'create'
    ? addEnergyMediumTypeApi(param.formData)
        .then(() => {
          message.success('新增成功');
          gridFormRef.value?.gridApi.reload();
          return true;
        })
        .catch(() => false)
    : editEnergyMediumTypeApi({
        ...param.raw,
        ...param.formData,
      })
        .then(() => {
          message.success('更新成功');
          gridFormRef.value?.gridApi.query();
          return true;
        })
        .catch(() => false);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="能源介质类型"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :disable-page="true"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '能源介质类型信息',
                meta: { type: 'create' },
              })
            "
          />
        </template>
        <template #action="{ row }">
          <Button
            type="link"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '能源介质类型信息',
                formData: row,
                meta: { type: 'edit' },
              })
            "
          >
            编辑
          </Button>
          <RemoveButton @confirm="deleteMediumType(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
