<!--
 * @Author: Strayer
 * @Date: 2025-07-04
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-01
 * @Description: 模态表单
-->
<script lang="ts" setup>
import type { UploadProps } from 'ant-design-vue';

import type { ExtendedModalApi, VbenFormProps } from '@vben/common-ui';

import type { CustomFormSchema } from '.';

import type { ComponentType } from '#/adapter/component';

import { computed, nextTick, ref, shallowRef, watch } from 'vue';

import AColorPicker from '@elonmuscle/antdv-color-picker';
import { useDebounceFn } from '@vueuse/core';
import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { clone, isObjectType, isString } from 'remeda';
import { v4 as uuidv4 } from 'uuid';

import { useVbenForm } from '#/adapter/form';
import Wangeditor from '#/components/wangeditor.vue';

import { formatFormOption } from '.';
import AddItem from './addItem.vue';
import UploadFile from './upload-file.vue';
import UploadImg from './upload-img.vue';

import '@elonmuscle/antdv-color-picker/dist/style.css';

const props = defineProps<{
  formConfig?: VbenFormProps<ComponentType>;
  formOption: CustomFormSchema[]; // 表单选项
  modalApi?: ExtendedModalApi;
  submit?: (param: { formData: any; meta: any; raw: any }) => void; // 提交数据的方法
}>();

// 富文本是否全屏，如果外部嵌套了modalApi则可传入用于两个组件全屏的同步
const wangEditorFullscreen = defineModel<boolean>('wangEditorFullscreen', {
  default: false,
});

// 传入的原始表单对象
const formDataOrigin = ref<any>({});

// open方法时传入的meta数据
const meta = shallowRef<any>();

// 格式化表单选项用
const keywordMap = ref<Record<string, string>>({});
const fetchingMap = ref<Record<string, boolean>>({});

// 最新的表单数据
const latestFormData = shallowRef<any>();

// 创建防抖函数来更新 latestFormData
const updateLatestFormData = useDebounceFn((value) => {
  latestFormData.value = value;
}, 300); // 300ms 的防抖延迟

const currentFormOption = computed(() =>
  formatFormOption(props.formOption, {
    keywordMap,
    fetchingMap,
    latestFormData,
  }),
);

const state = computed(() => props.modalApi?.useStore());

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 在label后显示一个冒号
    colon: true,
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onSubmit,
  schema: currentFormOption.value as any,
  showDefaultActions: false,
  handleValuesChange: (value) => updateLatestFormData(value),
  ...props.formConfig,
});

// 根据外部表单选项变化同步更新
watch(
  () => currentFormOption.value,
  (schema: any) => {
    formApi.setState(() => ({
      schema,
    }));
  },
);

watch(
  () => formDataOrigin.value,
  (value) => {
    for (const item of currentFormOption.value) {
      // 同步wangedit的内容给表单才能正确触发表单的验证
      if (item.component === 'Wangeditor') {
        nextTick(() => {
          formApi.setFieldValue(
            item.fieldName,
            // 特殊处理，wangedit编辑器的空值默认是<p><br></p>
            value[item.fieldName] === '<p><br></p>'
              ? ''
              : value[item.fieldName],
            true,
          );
        });
      } else if (item.component === 'ColorPicker') {
        nextTick(() => {
          formApi.setFieldValue(item.fieldName, value[item.fieldName], false);
        });
      }
    }
  },
  { deep: true },
);

// 到这里时表单内部的验证已通过
function onSubmit() {
  formApi.getValues().then(async (res) => {
    // 先检查所有的AddItem组件是否验证通过
    let addItemValidationFailed = false;

    for (const item of currentFormOption.value) {
      // 处理 AddItem
      if (item.component === 'Add') {
        // 先获取AddItem组件的表单API
        const addItemFormApi = addItemRefMap.value[item.fieldName]?.addFormApi;
        if (addItemFormApi) {
          // 验证AddItem组件内的表单
          const { valid } = await addItemFormApi.validate();
          if (!valid) {
            addItemValidationFailed = true;
            message.error(`${item.label || '子表单'}校验失败，请检查输入`);
            return; // 如果校验失败，直接返回，不继续处理
          }
        }

        res[item.fieldName] =
          await addItemRefMap.value[item.fieldName]?.getValue();
        if (item.rules === 'required' && !res[item.fieldName]) {
          message.error(`${item.label}不能为空`);
          return;
        }
      }
    }

    // 如果AddItem组件校验失败，不继续处理
    if (addItemValidationFailed) return;

    // 以下是原有的处理逻辑
    for (const item of currentFormOption.value) {
      // 处理 wangEditor 的值
      if (item.component === 'Wangeditor') {
        res[item.fieldName] =
          wangEditorRefMap.value[item.fieldName]?.getValues() ?? '';

        if (item.rules === 'required' && !res[item.fieldName]) {
          message.error(`${item.label}不能为空`);
          return;
        }
      }

      // 处理图片或文件
      if (['Upload', 'UploadImg'].includes(item.component as any)) {
        const fileDataType = item.custom?.fileDataType ?? 'string';

        if (fileDataType === 'string') {
          res[item.fieldName] =
            fileListMap.value[item.fieldName]?.[0]?.url ?? '';
        } else if (fileDataType === 'stringList') {
          res[item.fieldName] = fileListMap.value[item.fieldName]?.map(
            (item: any) => item.url,
          );
        } else {
          res[item.fieldName] = fileListMap.value[item.fieldName];
        }
      }

      // 如果日期并且只到天，则默认是00:00:00
      if (
        item.component === 'DatePicker' &&
        !(item.componentProps as any)?.showTime &&
        res?.[item.fieldName]
      ) {
        res[item.fieldName] = res[item.fieldName].startOf('day');
      }

      // 处理时间
      if (
        (item.component === 'DatePicker' || item.component === 'TimePicker') &&
        res?.[item.fieldName]
      ) {
        res[item.fieldName] = res[item.fieldName].toISOString();
      }

      // 处理时间范围
      if (item.component === 'RangePicker' && res?.[item.fieldName]) {
        // 如果开始时间比结束时间靠后，则交换位置
        if (
          res[item.fieldName][0] &&
          res[item.fieldName][1] &&
          res[item.fieldName][0].isAfter(res[item.fieldName][1])
        ) {
          res[item.fieldName] = res[item.fieldName].reverse();
        }

        // 如果不显示具体时间，则开始时间默认是一天开始的时候。结束时间默认是一天结束的时候
        if (!(item.componentProps as any)?.showTime) {
          const dateArr: Dayjs[] = [...(res[item.fieldName] ?? [])];
          if (dateArr[0]) {
            dateArr[0] = dateArr[0].startOf('day');
          }
          if (dateArr[1]) {
            dateArr[1] = dateArr[1].endOf('day');
          }

          res[item.fieldName] = dateArr;
        }

        res[item.custom?.rangePicker0 ?? 'startTime'] =
          res[item.fieldName][0].toISOString();
        res[item.custom?.rangePicker1 ?? 'endTime'] =
          res[item.fieldName][1].toISOString();
      }

      // 处理颜色选择器
      if (item.component === 'ColorPicker') {
        res[item.fieldName] = formDataOrigin.value[item.fieldName] ?? '';
      }
    }

    props.submit?.({
      formData: res,
      meta: meta.value,
      raw: formDataOrigin.value,
    });
  });
}

// ---------------------富文本编辑器begin--------------------------
// 之前的表单全屏状态
const prevFormFullScreen = ref(state.value?.value.fullscreen);

// 富文本编辑器
const wangEditorRefMap = ref<{
  [key: string]: InstanceType<typeof Wangeditor> | null;
}>({});

// 富文本编辑器是否全屏
const wangEditorFullscreenKey = ref('');
function wangEditorOpenFullScreen(key: string) {
  wangEditorFullscreenKey.value = key;
  prevFormFullScreen.value = state.value?.value.fullscreen;
  props.modalApi?.setState((prev) => {
    return { ...prev, fullscreen: true };
  });
  wangEditorFullscreen.value = true;
}
function wangEditorCloseFullScreen() {
  wangEditorFullscreenKey.value = '';
  wangEditorFullscreen.value = false;
  props.modalApi?.setState((prev) => {
    return { ...prev, fullscreen: prevFormFullScreen.value };
  });
}
// ---------------------富文本编辑器3nd-------------------------

// add表单
const addItemRefMap = ref<{
  [key: string]: InstanceType<typeof AddItem> | null;
}>({});

// 上传图片或文件
const fileListMap = ref<{ [key: string]: UploadProps['fileList'] }>({});

// 同步上传文件或图片数据
watch(
  () => fileListMap.value,
  (value) => {
    for (const key in value) {
      const formOption = currentFormOption.value.find(
        (item) => item.fieldName === key,
      );
      if (!formOption) return;

      const fileDataType = formOption.custom?.fileDataType ?? 'string';

      nextTick(() => {
        if (fileDataType === 'string') {
          formApi.setFieldValue(key, value[key]?.[0]?.url ?? '', true);
          return;
        } else if (fileDataType === 'stringList') {
          formApi.setFieldValue(
            key,
            value[key]?.map((item) => item.url),
            true,
          );
          return;
        }

        formApi.setFieldValue(key, value[key], true);
      });
    }
  },
  { deep: true },
);

// 提供给外部的打开弹窗方法
function setData(param?: { formData?: any; meta?: any }) {
  formDataOrigin.value = clone(param?.formData ?? {});
  const formData = clone(param?.formData) ?? {};

  fileListMap.value = {};
  for (const item of currentFormOption.value) {
    // 处理图片, 图片格式 必须是string 或 UploadProps['fileList']
    if (['Upload', 'UploadImg'].includes(item.component as any)) {
      fileListMap.value[item.fieldName] = [];

      if (formData[item.fieldName]) {
        fileListMap.value[item.fieldName] =
          !item.custom?.fileDataType || item.custom?.fileDataType === 'string'
            ? [
                {
                  uid: '-1',
                  name: 'file',
                  status: 'done',
                  url: isString(formData[item.fieldName])
                    ? formData[item.fieldName]
                    : '',
                },
              ]
            : formData[item.fieldName].map((fileValue: any) => {
                if (item.custom?.fileDataType === 'stringList') {
                  return {
                    uid: uuidv4(),
                    name: 'file',
                    status: 'done',
                    url: isString(fileValue) ? fileValue : '',
                  };
                }

                const fileObj: any = isObjectType(fileValue) ? fileValue : {};

                return {
                  uid: fileObj?.uid ?? uuidv4(),
                  name: fileObj?.name ?? 'file',
                  status: fileObj?.status ?? 'done',
                  url: fileObj?.url ?? '',
                };
              });
      }
    }

    // 处理时间
    if (
      (item.component === 'DatePicker' || item.component === 'TimePicker') &&
      formData[item.fieldName]
    ) {
      formData[item.fieldName] = dayjs(formData[item.fieldName]);
    }

    // 处理时间范围
    if (item.component === 'RangePicker' && formData) {
      const startTimeStr: string | undefined =
        formData[item.custom?.rangePicker0 ?? 'startTime'];
      const endTimeStr: string | undefined =
        formData[item.custom?.rangePicker1 ?? 'endTime'];

      let startTime = startTimeStr ? dayjs(startTimeStr) : undefined;
      let endTime = endTimeStr ? dayjs(endTimeStr) : undefined;

      // 如果开始时间比结束时间靠后，则交换位置
      if (startTime && endTime && startTime.isAfter(endTime)) {
        [startTime, endTime] = [endTime, startTime];
      }

      formData[item.fieldName] = [startTime, endTime];
    }

    // Add处理
    if (item.component === 'Add') {
      nextTick(() => {
        addItemRefMap.value[item.fieldName]?.setData(
          formData[item.fieldName] ?? [],
        );
      });
    }
  }

  latestFormData.value = formData;
  formApi.setValues(formData);
  meta.value = param?.meta;
}

/** 提交表单-会触发表单验证 */
async function launchSubmit() {
  return formApi.validateAndSubmitForm();
}

defineExpose({
  formApi,
  setData,
  launchSubmit,
});
</script>

<template>
  <Form>
    <template
      v-for="item in currentFormOption"
      :key="item.fieldName"
      #[item.fieldName]
    >
      <div v-if="item.component === 'Wangeditor'" class="wangeditor-wrapper">
        <Wangeditor
          :ref="
            (el) => {
              wangEditorRefMap[item.fieldName] = el as any;
            }
          "
          v-model:value="formDataOrigin[item.fieldName]"
          v-show="
            !wangEditorFullscreenKey ||
            wangEditorFullscreenKey === item.fieldName
          "
          :editor-config="{
            readOnly: formApi.state?.commonConfig?.disabled || item.disabled,
          }"
          :hide-tool-bar="
            formApi.state?.commonConfig?.disabled || item.disabled
          "
          @open-full-screen="wangEditorOpenFullScreen(item.fieldName)"
          @close-full-screen="wangEditorCloseFullScreen"
        />
      </div>
      <UploadImg
        v-if="item.component === 'UploadImg'"
        class="upload-img-wrapper"
        v-model:file-list="fileListMap[item.fieldName]"
        :form-item="item"
        :disabled="formApi.state?.commonConfig?.disabled"
      />
      <UploadFile
        v-if="item.component === 'Upload'"
        class="upload-wrapper"
        v-model:file-list="fileListMap[item.fieldName]"
        :form-item="item"
        :disabled="formApi.state?.commonConfig?.disabled"
      />
      <AddItem
        v-if="item.component === 'Add'"
        :ref="
          (el) => {
            addItemRefMap[item.fieldName] = el as any;
          }
        "
        :item-option="item"
        :disabled="formApi.state?.commonConfig?.disabled"
      />
      <!-- color-picker -->
      <AColorPicker
        v-if="item.component === 'ColorPicker'"
        v-model="formDataOrigin[item.fieldName]"
      />
    </template>
  </Form>
</template>
<style scoped>
.wangeditor-wrapper {
  :deep(.w-e-full-screen-container) {
    z-index: 9;
  }
}
</style>
