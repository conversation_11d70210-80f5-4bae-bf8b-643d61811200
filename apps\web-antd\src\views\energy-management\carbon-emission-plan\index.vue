<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CarbonEmissionPlan } from '#/api/core/energy-management/carbon-emission-plan';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { h, onMounted, ref, shallowRef, useTemplateRef } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addCarbonEmissionPlanApi,
  calculateCarbonEmissionPlanApi,
  deleteCarbonEmissionPlanApi,
  editCarbonEmissionPlanApi,
  getCarbonEmissionPlanListApi,
} from '#/api/core/energy-management/carbon-emission-plan';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getTopSearchScheme } from './data';
import Edit from './edit.vue';

onMounted(() => {
  gridFormRef.value?.gridApi.formApi.setValues({
    date: dayjs().startOf('year'),
  });
});

const [Modal, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: Edit,
});

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<CarbonEmissionPlan>> =
  shallowRef(getColumnsScheme());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<CarbonEmissionPlan>,
  fromData: any,
) {
  return getCarbonEmissionPlanListApi(param.page, {
    year: dayjs(fromData.date).year(),
  });
}

/**
 * @description: 删除
 */
function deleteCarbonEmissionPlan(row: CarbonEmissionPlan) {
  return deleteCarbonEmissionPlanApi(row.planId!).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  return param.meta.type === 'create'
    ? addCarbonEmissionPlanApi(param.formData)
        .then(() => {
          message.success('新增成功');
          gridFormRef.value?.gridApi.reload();
          return true;
        })
        .catch(() => false)
    : editCarbonEmissionPlanApi({
        ...param.raw,
        ...param.formData,
      })
        .then(() => {
          message.success('更新成功');
          gridFormRef.value?.gridApi.query();
          return true;
        })
        .catch(() => false);
}

function addOne() {
  modalApi
    .setData({
      title: '新增碳排放计划',
      type: 'create',
    })
    .open();
}

function editOne(row: CarbonEmissionPlan) {
  modalApi
    .setData({
      title: '编辑碳排放计划',
      formData: row,
      type: 'edit',
    })
    .open();
}

async function calculateOne(row: CarbonEmissionPlan) {
  try {
    await calculateCarbonEmissionPlanApi(row.planId!);
    message.success('计算成功');
    gridFormRef.value?.gridApi.reload();
  } catch {
    message.error('计算失败');
  }
}

function handleConfirm() {
  gridFormRef.value?.gridApi.reload();
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        :columns-scheme="columnsScheme"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="addOne"
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="calculateOne(row)"> 计算 </Button>
          <Button type="link" @click="editOne(row)"> 编辑 </Button>
          <RemoveButton @confirm="deleteCarbonEmissionPlan(row)" />
        </template>
      </GridForm>
    </template>
    <Modal @confirm="handleConfirm" />
  </Page>
</template>
