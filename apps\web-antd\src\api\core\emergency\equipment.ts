import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * 应急装备对象
 */
export type EmergencyEquipment = {
  /**
   * 装备品牌
   */
  brand?: string;
  /**
   * 图片信息
   */
  cmEquipmentImageList?: CmEquipmentImage[];
  /**
   * 库存信息
   */
  cmInventory?: CmInventory;
  /**
   * 记录创建时间
   */
  createTime?: string;
  /**
   * 系统生成的唯一标识（主键）
   */
  equipmentId: string;
  /**
   * 生产厂家名称
   */
  manufacturer?: string;
  /**
   * 装备的名称
   */
  name?: string;
  /**
   * 关于装备的额外说明
   */
  remarks?: string;
  /**
   * 关联用户表（责任人外键）
   */
  responsibleId?: string;
  /**
   * 装备的规格型号
   */
  specification?: string;
  /**
   * 装备状态（normal:正常）
   */
  status?: string;
  /**
   * 关联装备类型表（外键）
   */
  typeId?: string;
  /**
   * 装备的计量单位
   */
  unit?: string;
  /**
   * 关联单位表（外键）
   */
  unitId?: string;
  /**
   * 记录更新时间
   */
  updateTime?: string;
};

/**
 * CmEquipmentImage对象
 *
 * CmEquipmentImage
 */
export type CmEquipmentImage = {
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 关联装备表（外键）
   */
  equipmentId: string;
  /**
   * 图片唯一标识
   */
  imageId: string;
  /**
   * 图片原始名称
   */
  imageName?: string;
  /**
   * 图片存储路径
   */
  imagePath?: string;
  /**
   * 图片大小（字节，默认0）
   */
  imageSize?: number;
  /**
   * 图片类型（如PNG、JPEG）
   */
  imageType?: string;
  /**
   * 是否为主图（1:是，0:否）
   */
  isMain?: number;
  /**
   * 图片排序号（默认0）
   */
  sortOrder?: number;
};

/**
 * 库存信息
 *
 * CmInventory
 */
export type CmInventory = {
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 关联装备表
   */
  equipmentId: string;
  /**
   * 库存记录唯一标识
   */
  inventoryId: string;
  /**
   * 关联存放位置表
   */
  locationId?: string;
  /**
   * 装备购买日期
   */
  purchaseDate?: string;
  /**
   * 采购价格(元)（默认0.00）
   */
  purchasePrice?: number;
  /**
   * 当前库存数量（默认0）
   */
  quantity?: number;
  /**
   * 安全库存数量（默认0）
   */
  safetyStock?: number;
  /**
   * 装备保质期(月)（默认0）
   */
  shelfLife?: number;
  /**
   * 装备入库日期
   */
  storageDate?: string;
  /**
   * 供应商名称
   */
  supplier?: string;
  /**
   * 记录更新时间（自动更新）
   */
  updateTime?: string;
};

/**
 * 获取装备列表
 */
export async function getEmergencyEquipmentListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: EmergencyEquipment[];
    total: number;
  }>('/cm-equipment-manage', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/**
 * 获取装备详情
 */
export async function getEmergencyEquipmentDetailApi(equipmentId: string) {
  return requestClient.get<EmergencyEquipment>(
    `/cm-equipment-manage/detail/${equipmentId}`,
  );
}

/**
 * 新增装备
 */
export async function addEmergencyEquipmentApi(data: EmergencyEquipment) {
  return requestClient.post<EmergencyEquipment>('/cm-equipment-manage', data);
}

/**
 * 修改装备
 */
export async function editEmergencyEquipmentApi(data: EmergencyEquipment) {
  return requestClient.put<EmergencyEquipment>('/cm-equipment-manage', data);
}

/**
 * 删除装备
 */
export async function deleteEmergencyEquipmentApi(equipmentId: string) {
  return requestClient.delete<EmergencyEquipment>(
    `/cm-equipment-manage/${equipmentId}`,
  );
}
