import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * CmDrillBasicInfo
 */
export type CmDrillBasicInfo = {
  /**
   * 优点
   */
  advantage?: string;
  /**
   * 附件
   */
  cmDrillAttachments?: CmDrillAttachments;
  /**
   * 资源使用
   */
  cmDrillResourceUsageList?: CmDrillResourceUsage[];
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 评估部门
   */
  department?: string;
  /**
   * 演练描述
   */
  description?: string;
  /**
   * 演练ID
   */
  drillId: string;
  /**
   * 参与人员
   */
  drillParticipantsList?: CmDrillParticipants[];
  /**
   * 步骤
   */
  drillStepRecordsList?: DrillStepRecords[];
  /**
   * 演练开始时间
   */
  drillTime?: string;
  /**
   * 演练时长(小时)
   */
  duration?: number;
  /**
   * 评估的结果（如优、良、中、差等）
   */
  evaluationResult?: string;
  /**
   * 分数
   */
  fraction?: string;
  /**
   * 意见
   */
  idea?: string;
  /**
   * 不足
   */
  insufficient?: string;
  /**
   * 演练地点
   */
  location?: string;
  /**
   * 演练目标
   */
  objectives?: string;
  /**
   * 总结
   */
  opinion?: string;
  /**
   * 负责组织演练的单位
   */
  organizer?: string;
  /**
   * 评估负责人
   */
  person?: string;
  /**
   * 评分明细
   */
  rating?: string;
  /**
   * 建议
   */
  suggestion?: string;
  /**
   * 演练标题
   */
  title?: string;
  /**
   * 类型（综合/专项等）
   */
  type?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 评估时间
   */
  valuationTime?: string;
};

/**
 * 附件
 *
 * CmDrillAttachments
 */
export type CmDrillAttachments = {
  /**
   * 附件唯一标识
   */
  attachmentId: string;
  /**
   * 关联演练基本信息表
   */
  drillId: string;
  /**
   * 附件原名称
   */
  name?: string;
  /**
   * 附件存储路径
   */
  path?: string;
  /**
   * 附件大小（单位：KB）
   */
  size?: number;
  /**
   * 附件类型（图片/文档等）
   */
  type?: string;
  /**
   * 上传附件的人员
   */
  uploader?: string;
  /**
   * 附件上传时间
   */
  uploadTime?: string;
};

/**
 * CmDrillResourceUsage对象
 *
 * CmDrillResourceUsage
 */
export type CmDrillResourceUsage = {
  /**
   * 关联演练基本信息表
   */
  drillId: string;
  /**
   * 资源名称
   */
  name?: string;
  /**
   * 使用资源的数量
   */
  quantity?: number;
  /**
   * 资源使用记录唯一标识
   */
  resourceId: string;
  /**
   * 资源使用后的状态
   */
  status?: string;
  /**
   * 资源类型(人力/物资等)
   */
  type?: string;
  /**
   * 资源使用情况说明
   */
  usageDesc?: string;
};

/**
 * CmDrillParticipants对象
 *
 * CmDrillParticipants
 */
export type CmDrillParticipants = {
  /**
   * 参与人员联系电话
   */
  contact?: string;
  /**
   * 参与人员所属单位或部门
   */
  department?: string;
  /**
   * 关联演练基本信息表
   */
  drillId: string;
  /**
   * 参与人员姓名
   */
  name?: string;
  /**
   * 参与人员记录唯一标识
   */
  participantId: string;
  /**
   * 其他相关信息
   */
  remarks?: string;
  /**
   * 参与人员在演练中的角色
   */
  role?: string;
};

/**
 * DrillStepRecords对象
 *
 * DrillStepRecords
 */
export type DrillStepRecords = {
  /**
   * 关联演练基本信息表
   */
  drillId: string;
  /**
   * 时长
   */
  duration?: string;
  /**
   * 步骤结束时间
   */
  endTime?: string;
  /**
   * 步骤执行情况总结
   */
  execution?: string;
  /**
   * 步骤开始时间
   */
  startTime?: string;
  /**
   * 步骤详细描述
   */
  stepDesc?: string;
  /**
   * 步骤记录唯一标识
   */
  stepId: string;
  /**
   * 步骤名称
   */
  stepName?: string;
  /**
   * 步骤执行顺序
   */
  stepOrder?: number;
  /**
   * 目标
   */
  target?: string;
};

/** 获取演练评估列表 */
export async function getDrillEvaluateListApi(
  assessment: boolean, // assessment true是待评估,false已评估
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: CmDrillBasicInfo[];
    total: number;
  }>('/cm-drill-basic-info', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
      assessment,
    },
  });
}

/** 获取演练评估详情 */
export async function getDrillEvaluateDetailApi(drillId: string) {
  return requestClient.get<CmDrillBasicInfo>(
    `/cm-drill-basic-info/detail/${drillId}`,
  );
}

/** 新增演练评估 */
export async function addDrillEvaluateApi(data: CmDrillBasicInfo) {
  return requestClient.post<CmDrillBasicInfo>('/cm-drill-basic-info', data);
}

/** 修改演练评估 */
export async function editDrillEvaluateApi(data: CmDrillBasicInfo) {
  return requestClient.put<CmDrillBasicInfo>('/cm-drill-basic-info', data);
}

/** 删除演练评估 */
export async function deleteDrillEvaluateApi(drillId: string) {
  return requestClient.delete<CmDrillBasicInfo>(
    `/cm-drill-basic-info/${drillId}`,
  );
}

/** 统计 */
export async function getDrillEvaluateCountApi() {
  return requestClient.post<{
    /**
     * 平均得分
     */
    averageScore?: number;
    /**
     * 平均得分较上月变化百分比
     */
    averageScoreChangePercentage?: number;
    /**
     * 评估通过率
     */
    passRate?: number;
    /**
     * 评估通过率较上月变化百分比
     */
    passRateChangePercentage?: number;
    /**
     * 当月总数
     */
    totalCount?: number;
    /**
     * ----- 新增字段开始 (用于存储较上月比较的百分比) -----
     * 总数较上月变化百分比
     */
    totalCountChangePercentage?: number;
    /**
     * 日期类型map
     */
    typeDateMap?: Record<string, Record<string, number>>;
    /**
     * 待评估数
     */
    waitEvaluate?: number;
    /**
     * 待评估数较上月变化百分比
     */
    waitEvaluateChangePercentage?: number;
  }>('/cm-drill-basic-info/statistics');
}
