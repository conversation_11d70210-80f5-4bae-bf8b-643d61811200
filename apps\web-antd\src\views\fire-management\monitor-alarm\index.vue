<script lang="ts" setup>
import type { ShallowRef } from 'vue';
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { FfMonitoringAlarm } from '#/api/core/fire-management/monitor-alarm';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addMonitoringAlarmApi,
  deleteMonitoringAlarmApi,
  editMonitoringAlarmApi,
  getMonitoringAlarmListApi,
} from '#/api/core/fire-management/monitor-alarm';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<FfMonitoringAlarm>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

const router = useRouter();

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<FfMonitoringAlarm>,
  fromData: any,
) {
  return getMonitoringAlarmListApi(param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: FfMonitoringAlarm) {
  return deleteMonitoringAlarmApi(row.deviceId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: FfMonitoringAlarm) {
  gridFormRef.value?.formModalRef?.open({
    title: '设备详情',
    formData: row,
    meta: { type: 'edit', raw: row },
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: FfMonitoringAlarm;
}): Promise<boolean> {
  if (param.meta.type === 'create')
    addMonitoringAlarmApi({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editMonitoringAlarmApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="设备列表"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增设备',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <Button
            type="link"
            @click="
              router.push(
                `/fire-management/monitor-alarm/alarm-log/${row.deviceId}`,
              )
            "
          >
            告警记录
          </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
