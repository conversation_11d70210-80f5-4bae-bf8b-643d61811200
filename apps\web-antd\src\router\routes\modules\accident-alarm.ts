/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-16
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-29
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\router\routes\modules\accident-alarm.ts
 */
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'svg:accident',
      title: '事故接警',
    },
    name: 'AccidentAlarm',
    path: '/accident-alarm',
    redirect: '/accident-alarm/manage',
    children: [
      {
        meta: {
          title: '事件预警管理',
        },
        name: 'AccidentAlarmManage',
        path: '/accident-alarm/manage',
        component: () => import('#/views/accident-alarm/manage/index.vue'),
      },
      {
        meta: {
          title: '事件预警详情',
          hideInMenu: true,
          activePath: '/accident-alarm/manage',
        },
        name: 'AccidentAlarmManageDetail',
        path: '/accident-alarm/manage/detail/:parentId',
        props: true,
        component: () =>
          import('#/views/accident-alarm/manage/detail/index.vue'),
      },
      {
        meta: {
          title: '事件通知',
        },
        name: 'AccidentAlarmNotice',
        path: '/accident-alarm/notice',
        props: true,
        component: () => import('#/views/accident-alarm/notice/index.vue'),
      },
    ],
  },
];

export default routes;
