import type { CmDrillBasicInfo } from '#/api/core/emergency-drill/drill-evaluate';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-01
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency-drill\drill-record\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 状态
export const StatusMap: Record<
  string,
  { color: string; label: string; value: string }
> = {
  优: {
    value: '优',
    label: '优',
    color: 'gold',
  },
  良: {
    value: '良',
    label: '良',
    color: 'green',
  },
  中: {
    value: '中',
    label: '中',
    color: 'blue',
  },
  差: {
    value: '差',
    label: '差',
    color: 'red',
  },
};

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '演练标题',
    },
    {
      fieldName: 'type',
      label: '演练类型',
    },
    {
      fieldName: 'status',
      label: '演练结果',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },
    {
      fieldName: 'timeRange',
      label: '时间范围',
      component: 'RangePicker',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CmDrillBasicInfo> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'title',
      title: '演练标题',
      customType: 'userInfo',
      custom: {
        userInfo: {
          name: (row: CmDrillBasicInfo) => row.title ?? '',
          desc: (row: CmDrillBasicInfo) => `编号：${row.drillId}`,
        },
      },
    },
    // 演练类型
    {
      field: 'type',
      title: '演练类型',
    },
    // 组织单位
    {
      field: 'organizer',
      title: '组织单位',
    },
    // 演练时间
    {
      field: 'drillTime',
      title: '演练时间',
      customType: 'date',
    },
    // 演练结果
    {
      field: 'evaluationResult',
      title: '演练结果',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.evaluationResult;
        return [StatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    // 评估分数
    {
      field: 'fraction',
      title: '评估分数',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      component: 'Divider',
      fieldName: '',
      label: '演练基本信息',
    },
    {
      fieldName: 'title',
      label: '演练标题',
      rules: 'required',
    },
    // 演练类型
    {
      fieldName: 'type',
      label: '演练类型',
      rules: 'required',
    },
    // 演练地点
    {
      fieldName: 'location',
      label: '演练地点',
      rules: 'required',
    },
    // 演练时间
    {
      fieldName: 'drillTime',
      label: '演练时间',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        showTime: true,
      },
    },
    // 演练时长
    {
      fieldName: 'duration',
      label: '时长(小时)',
      rules: 'required',
    },
    // 组织单位
    {
      fieldName: 'organizer',
      label: '组织单位',
      rules: 'required',
    },
    // 演练目标
    {
      fieldName: 'objectives',
      label: '演练目标',
      rules: 'required',
      component: 'Textarea',
    },
    // 演练描述
    {
      fieldName: 'description',
      label: '演练描述',
      component: 'Textarea',
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '参与人员',
    },
    {
      fieldName: 'drillParticipantsList',
      label: '参与人员',
      component: 'Add',
      custom: {
        addFormConfig: {
          wrapperClass: 'grid-cols-2',
        },
        addObj: [
          {
            fieldName: 'name',
            label: '姓名',
            rules: 'required',
            formItemClass: 'col-span-2',
          },
          {
            fieldName: 'department',
            label: '部门',
            rules: 'required',
          },
          {
            fieldName: 'role',
            label: '角色',
            rules: 'required',
          },
        ],
      },
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '演练步骤',
    },
    {
      fieldName: 'drillStepRecordsList',
      label: '演练步骤',
      component: 'Add',
      custom: {
        addFormConfig: {
          wrapperClass: 'grid-cols-2',
        },
        addObj: [
          {
            fieldName: 'stepName',
            label: '步骤标题',
            rules: 'required',
          },
          {
            fieldName: 'duration',
            label: '预计时长(分钟)',
            rules: 'required',
          },
          {
            fieldName: 'stepDesc',
            label: '步骤描述',
            component: 'Textarea',
            rules: 'required',
          },
          {
            fieldName: 'target',
            label: '预期目标',
            component: 'Textarea',
          },
        ],
      },
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '资源需求',
    },
    {
      fieldName: 'cmDrillResourceUsageList',
      label: '资源需求',
      component: 'Add',
      custom: {
        addFormConfig: {
          wrapperClass: 'grid-cols-3',
        },
        addObj: [
          {
            fieldName: 'type',
            label: '资源类型',
            rules: 'required',
          },
          {
            fieldName: 'name',
            label: '资源名称',
            rules: 'required',
          },
          {
            fieldName: 'quantity',
            label: '数量',
            rules: 'required',
            component: 'InputNumber',
          },
          {
            fieldName: 'usageDesc',
            label: '资源描述',
            component: 'Textarea',
          },
        ],
      },
    },
    // {
    //   component: 'Divider',
    //   fieldName: '',
    //   label: '演练评估',
    // },
    // {
    //   fieldName: 'evaluationResult',
    //   label: '评估结果',
    //   rules: 'required',
    //   component: 'Select',
    //   componentProps: {
    //     options: Object.values(StatusMap),
    //   },
    // },
    // {
    //   fieldName: 'fraction',
    //   label: '评估分数(0-100)',
    //   rules: 'required',
    //   component: 'InputNumber',
    // },
    // {
    //   fieldName: 'idea',
    //   label: '评估意见',
    //   component: 'Textarea',
    // },
    // {
    //   fieldName: 'suggestion',
    //   label: '改进建议',
    //   component: 'Textarea',
    // },
    {
      component: 'Divider',
      fieldName: '',
      label: '演练附件',
    },
    {
      fieldName: 'cmDrillAttachments',
      label: '上传附件',
      component: 'Upload',
    },
  ];
}
