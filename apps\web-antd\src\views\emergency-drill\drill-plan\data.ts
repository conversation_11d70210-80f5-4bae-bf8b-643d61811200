import type { CmDrillPlan } from '#/api/core/emergency-drill/drill-plan';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-31
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency-drill\drill-plan\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { getDrillEvaluateListApi } from '#/api/core/emergency-drill/drill-evaluate';
import { getEmergencyPlanListApi } from '#/api/core/emergency-plan/manage';

// 状态
export const StatusMap: Record<
  string,
  { color: string; label: string; value: string }
> = {
  计划中: {
    value: '计划中',
    label: '计划中',
    color: 'cyan',
  },
  进行中: {
    value: '进行中',
    label: '进行中',
    color: 'blue',
  },
  已完成: {
    value: '已处理',
    label: '已处理',
    color: 'green',
  },
};

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '演练计划标题',
    },
    {
      fieldName: 'type',
      label: '演练类型',
    },
    {
      fieldName: 'status',
      label: '演练状态',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },
    {
      fieldName: 'timeRange',
      label: '时间范围',
      component: 'RangePicker',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CmDrillPlan> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'title',
      title: '演练计划标题',
      customType: 'userInfo',
      custom: {
        userInfo: {
          name: (row: CmDrillPlan) => row.title ?? '',
          desc: (row: CmDrillPlan) => `编号：${row.planId}`,
        },
      },
    },
    // 演练类型
    {
      field: 'drillType',
      title: '演练类型',
    },
    // 演练时间
    {
      field: 'drillTime',
      title: '演练时间',
      customType: 'date',
    },
    // 组织单位
    {
      field: 'organizer',
      title: '组织单位',
    },
    // 演练状态
    {
      field: 'status',
      title: '演练状态',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.status;
        return [StatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      component: 'Divider',
      fieldName: '',
      label: '演练计划基本信息',
    },
    {
      fieldName: 'title',
      label: '演练计划标题',
      rules: 'required',
    },
    // 演练类型
    {
      fieldName: 'drillType',
      label: '演练类型',
      rules: 'required',
    },
    // 演练等级
    {
      fieldName: 'level',
      label: '演练等级',
      rules: 'required',
    },
    // 演练目的
    {
      fieldName: 'objectives',
      label: '演练目的',
      rules: 'required',
    },
    // 演练地点
    {
      fieldName: 'location',
      label: '演练地点',
      rules: 'required',
    },
    // 演练时间
    {
      fieldName: 'drillTime',
      label: '演练时间',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        showTime: true,
      },
    },
    // 演练时长
    {
      fieldName: 'duration',
      label: '时长(小时)',
      rules: 'required',
    },
    // 组织单位
    {
      fieldName: 'organizer',
      label: '演练组织单位',
      rules: 'required',
    },
    // 演练状态
    {
      fieldName: 'status',
      label: '演练状态',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '演练主题与预案关联',
    },
    // 演练主题
    {
      fieldName: 'subject',
      label: '演练主题',
      rules: 'required',
    },
    // 关联预案
    {
      fieldName: 'cmplanId',
      label: '关联应急预案',
      rules: 'required',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getEmergencyPlanListApi(
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              planName: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.planName ?? '',
            value: item.planId,
          }));
        },
      },
    },
    // 关联历史案例
    {
      fieldName: 'oldId',
      label: '关联历史案例',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getDrillEvaluateListApi(
            false,
            {
              currentPage: 1,
              pageSize: 999_999,
            },
            {
              planName: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.title ?? '',
            value: item.drillId,
          }));
        },
      },
    },
    // 演练目标与要求
    {
      fieldName: 'detail',
      label: '目标与要求',
      component: 'Textarea',
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '演练步骤',
    },
    {
      fieldName: 'drillSteps',
      label: '演练步骤',
      component: 'Add',
      custom: {
        addFormConfig: {
          wrapperClass: 'grid-cols-2',
        },
        addObj: [
          {
            fieldName: 'title',
            label: '步骤标题',
            rules: 'required',
          },
          {
            fieldName: 'description',
            label: '步骤描述',
            component: 'Textarea',
          },
          {
            fieldName: 'department',
            label: '负责部门',
          },
          {
            fieldName: 'duration',
            label: '预计时长(分钟)',
          },
        ],
      },
    },
  ];
}
