/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-01
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\place\data.ts
 */
import type { EmergencyPlace } from '#/api/core/emergency/place';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '场所名称',
    },
    {
      fieldName: 'type',
      label: '场所类型',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EmergencyPlace> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'facilityId',
      title: '场所编码',
    },
    {
      field: 'name',
      title: '场所名称',
    },
    {
      field: 'type',
      title: '场所类型',
    },
    {
      field: 'area',
      title: '容纳面积(㎡)',
    },
    {
      field: 'capacity',
      title: '可容纳人数',
    },
    {
      field: 'responsible',
      title: '责任人',
    },
    {
      field: 'contact',
      title: '联系电话',
    },
    {
      field: 'emergencyTag',
      title: '应急标识',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '场所名称',
      rules: 'required',
    },
    {
      fieldName: 'type',
      label: '场所类型',
      rules: 'required',
    },
    {
      label: '责任人',
      fieldName: 'responsible',
      rules: 'required',
    },
    {
      label: '联系电话',
      fieldName: 'contact',
      rules: 'required',
    },
    {
      label: '地址',
      fieldName: 'address',
      rules: 'required',
    },
    // 经度
    {
      fieldName: 'longitude',
      label: '经度',
      rules: 'required',
      component: 'InputNumber',
    },
    // 纬度
    {
      fieldName: 'latitude',
      label: '纬度',
      rules: 'required',
      component: 'InputNumber',
    },
    {
      label: '应急标识',
      fieldName: 'emergencyTag',
      rules: 'required',
    },
    {
      label: '主管单位',
      fieldName: 'supervisor',
    },
    {
      label: '容纳面积(㎡)',
      fieldName: 'area',
      rules: 'required',
    },
    {
      label: '可容纳人数',
      fieldName: 'capacity',
      rules: 'required',
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '排水状况',
    },
    {
      label: '排水系统类型',
      fieldName: 'cmFacilityDrainageListSystemType',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '合流制', value: '合流制' },
          { label: '分流制', value: '分流制' },
          { label: '混合制', value: '混合制' },
        ],
      },
    },
    {
      label: '排水能力(m³/h)',
      fieldName: 'cmFacilityDrainageListDrainageCap',
      component: 'InputNumber',
    },
    {
      label: '排水系统状况',
      fieldName: 'cmFacilityDrainageListCondition',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '良好', value: '良好' },
          { label: '一般', value: '一般' },
          { label: '较差', value: '较差' },
        ],
      },
    },
    {
      label: '排水系统描述',
      fieldName: 'cmFacilityDrainageListDescription',
      component: 'Textarea',
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '消防设施状况',
    },
    {
      label: '消防设施完备',
      fieldName: 'cmFacilityFireSafetyListIsComplete',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
    },
    {
      label: '消防设施描述',
      fieldName: 'cmFacilityFireSafetyListDescription',
      component: 'Textarea',
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '通风状况',
    },
    {
      label: '通风系统类型',
      fieldName: 'cmFacilityVentilationListSystemType',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '自然通风', value: '自然通风' },
          { label: '机械通风', value: '机械通风' },
          { label: '混合通风', value: '混合通风' },
        ],
      },
    },
    {
      label: '通风系统描述',
      fieldName: 'cmFacilityVentilationListDescription',
      component: 'Textarea',
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '应急物资状况',
    },
    {
      label: '应急物资储备',
      fieldName: 'cmFacilitySupplyStatusListSupplyLevel',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '充足', value: '充足' },
          { label: '足够', value: '足够' },
          { label: '不足', value: '不足' },
        ],
      },
    },
    {
      label: '应急物资类型',
      fieldName: 'cmFacilitySupplyStatusListSupplyTypes',
      component: 'CheckboxGroup',
      componentProps: {
        mode: 'multiple',
        options: [
          { label: '食品', value: '食品' },
          { label: '饮用水', value: '饮用水' },
          { label: '药品', value: '药品' },
          { label: '设备', value: '设备' },
          { label: '衣物', value: '衣物' },
          { label: '其他', value: '其他' },
        ],
      },
    },
    {
      label: '应急物资描述',
      fieldName: 'cmFacilitySupplyStatusListDescription',
      component: 'Textarea',
    },
    {
      component: 'Divider',
      fieldName: '',
      label: '场所照片',
    },
    {
      label: '场所照片',
      fieldName: 'cmFacilityPhotoListPhotoPath',
      component: 'UploadImg',
    },
  ];
}
