import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * CmFacilityInfo对象
 *
 * EmergencyPlace
 */
export type EmergencyPlace = {
  /**
   * 场所详细地址
   */
  address?: string;
  /**
   * 场所面积(平方米)（默认0.00）
   */
  area?: number;
  /**
   * 场所可容纳人数（默认0）
   */
  capacity?: number;
  /**
   * 场所环境排水状况
   */
  cmFacilityDrainage?: CmFacilityDrainage;
  /**
   * 场所消防设施状况
   */
  cmFacilityFireSafety?: CmFacilityFireSafety;
  /**
   * 场所照片
   */
  cmFacilityPhoto?: CmFacilityPhoto;
  /**
   * 场所应急物资状况
   */
  cmFacilitySupplyStatus?: CmFacilitySupplyStatus;
  /**
   * 场所通风状况
   */
  cmFacilityVentilation?: CmFacilityVentilation;
  /**
   * 责任人联系电话
   */
  contact?: string;
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 应急标识（如：应急中心/避难场所等）
   */
  emergencyTag?: string;
  /**
   * 场所唯一标识
   */
  facilityId: string;
  /**
   * 纬度
   */
  latitude?: number;
  /** 经度 */
  longitude?: number;
  /**
   * 场所的正式名称
   */
  name?: string;
  /**
   * 场所负责人姓名
   */
  responsible?: string;
  /**
   * 场所的主管单位名称
   */
  supervisor?: string;
  /**
   * 场所类型（如：医疗机构/学校/居民区等）
   */
  type?: string;
  /**
   * 记录更新时间（自动更新）
   */
  updateTime?: string;
};

/**
 * 场所环境排水状况
 *
 * CmFacilityDrainage
 */
export type CmFacilityDrainage = {
  /**
   * 排水系统状况（良好/一般/较差）
   */
  condition?: string;
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 排水系统详细描述
   */
  description?: string;
  /**
   * 排水能力(m³/h)（默认0.00）
   */
  drainageCap?: number;
  /**
   * 关联场所基本信息表（外键）
   */
  facilityId: string;
  /**
   * 记录唯一标识（主键）
   */
  recordId: string;
  /**
   * 排水系统类型（合流制/分流制/混合制）
   */
  systemType?: string;
};

/**
 * 场所消防设施状况
 *
 * CmFacilityFireSafety
 */
export type CmFacilityFireSafety = {
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 消防设施详细描述
   */
  description?: string;
  /**
   * 关联场所基本信息表（外键）
   */
  facilityId: string;
  /**
   * 消防设施是否完备（FALSE-否，TRUE-是）
   */
  isComplete?: boolean;
  /**
   * 记录唯一标识（主键）
   */
  recordId: string;
};

/**
 * 场所照片
 *
 * CmFacilityPhoto
 */
export type CmFacilityPhoto = {
  /**
   * 照片简要描述（最大长度100字符）
   */
  description?: string;
  /**
   * 关联场所基本信息表（外键）
   */
  facilityId: string;
  /**
   * 照片唯一标识（主键）
   */
  photoId: string;
  /**
   * 照片存储路径
   */
  photoPath?: string;
  /**
   * 照片显示顺序（默认0）
   */
  sortOrder?: number;
  /**
   * 照片上传时间（自动记录）
   */
  uploadTime?: string;
};

/**
 * 场所应急物资状况
 *
 * CmFacilitySupplyStatus
 */
export type CmFacilitySupplyStatus = {
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 应急物资详细描述
   */
  description?: string;
  /**
   * 关联场所基本信息表（外键）
   */
  facilityId: string;
  /**
   * 记录唯一标识（主键）
   */
  recordId: string;
  /**
   * 应急物资储备状况：充足/足够/不足
   */
  supplyLevel?: string;
  /**
   * 应急物资类型（如：食品,饮用水,药品）
   */
  supplyTypes?: string;
};

/**
 * 场所通风状况
 *
 * CmFacilityVentilation
 */
export type CmFacilityVentilation = {
  /**
   * 记录创建时间（自动生成）
   */
  createTime?: string;
  /**
   * 通风系统详细描述
   */
  description?: string;
  /**
   * 关联场所基本信息表（外键）
   */
  facilityId: string;
  /**
   * 记录唯一标识（主键）
   */
  recordId: string;
  /**
   * 通风系统类型（自然通风/机械通风/混合通风）
   */
  systemType?: string;
};

/** 获取应急场所列表 */
export async function getEmergencyPlaceListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: EmergencyPlace[];
    total: number;
  }>('/cm-facility-info', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 获取应急场所详情 */
export async function getEmergencyPlaceDetailApi(facilityId: string) {
  return requestClient.get<EmergencyPlace>(
    `/cm-facility-info/detail/${facilityId}`,
  );
}

/** 新增应急场所 */
export async function addEmergencyPlaceApi(data: EmergencyPlace) {
  return requestClient.post<EmergencyPlace>('/cm-facility-info', data);
}

/** 修改应急场所 */
export async function editEmergencyPlaceApi(data: EmergencyPlace) {
  return requestClient.put<EmergencyPlace>('/cm-facility-info', data);
}

/** 删除应急场所 */
export async function deleteEmergencyPlaceApi(facilityId: string) {
  return requestClient.delete<EmergencyPlace>(
    `/cm-facility-info/${facilityId}`,
  );
}
