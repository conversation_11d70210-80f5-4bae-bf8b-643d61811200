import type { EmergencyExpert } from '#/api/core/emergency/expert';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-01
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\expert\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '专家名称',
    },
    {
      fieldName: 'type',
      label: '擅长处理类型',
    },
    {
      fieldName: 'level',
      label: '专家级别',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EmergencyExpert> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'expertCode',
      title: '专家编码',
    },
    {
      field: 'name',
      title: '名称',
      customType: 'userInfo',
      custom: {
        userInfo: {
          name: (row: EmergencyExpert) => row.name ?? '',
          img: (row: EmergencyExpert) => row.photoPath ?? '',
          desc: (row: EmergencyExpert) => row.email ?? '',
        },
      },
    },
    {
      field: 'gender',
      title: '性别',
    },
    {
      field: 'age',
      title: '年龄',
    },
    {
      field: 'specialty',
      title: '专业领域',
    },
    {
      field: 'expertiseType',
      title: '擅长处理类型',
    },
    {
      field: 'expertLevel',
      title: '专家级别',
    },
    {
      field: 'phone',
      title: '联系电话',
    },
    {
      field: 'status',
      title: '状态',
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '专家名称',
      rules: 'required',
    },
    {
      fieldName: 'gender',
      label: '性别',
      rules: 'required',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '男', value: '男' },
          { label: '女', value: '女' },
        ],
      },
    },
    {
      fieldName: 'age',
      label: '年龄',
      rules: 'required',
      component: 'InputNumber',
    },
    {
      fieldName: 'phone',
      label: '联系电话',
      rules: 'required',
    },
    {
      fieldName: 'email',
      label: '邮箱',
      rules: 'required',
    },
    {
      fieldName: 'status',
      label: '状态',
      rules: 'required',
    },
    {
      fieldName: 'specialty',
      label: '专业领域',
      rules: 'required',
    },
    {
      fieldName: 'expertiseType',
      label: '擅长处理类型',
      rules: 'required',
    },
    {
      fieldName: 'expertLevel',
      label: '专家级别',
      rules: 'required',
    },
    {
      fieldName: 'workUnit',
      label: '工作单位',
      rules: 'required',
    },
    {
      fieldName: 'title',
      label: '职称',
    },
    {
      fieldName: 'position',
      label: '职务',
      rules: 'required',
    },
    {
      fieldName: 'description',
      label: '简介',
      component: 'Textarea',
    },
    {
      fieldName: 'photoPath',
      label: '照片',
      component: 'UploadImg',
    },
  ];
}
