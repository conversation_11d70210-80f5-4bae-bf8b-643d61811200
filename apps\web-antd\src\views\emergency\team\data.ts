/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-26
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\team\data.ts
 */
import type { CmTeamInfo } from '#/api/core/emergency/team';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 队伍状态
export const TeamStatusMap: Record<
  string,
  { color: string; label: string; value: string }
> = {
  waiting: {
    value: 'waiting',
    label: '待命',
    color: 'green',
  },
  // 执行任务
  executing: {
    value: 'executing',
    label: '执行任务',
    color: 'orange',
  },
  // 调度中
  dispatching: {
    value: 'dispatching',
    label: '调度中',
    color: 'blue',
  },
};

// 装备状态
export const EquipmentStatusMap: Record<
  string,
  { color: string; label: string; value: string }
> = {
  normal: {
    value: 'normal',
    label: '正常',
    color: 'green',
  },
  // 维护中
  maintaining: {
    value: 'maintaining',
    label: '维护中',
    color: 'orange',
  },
  // 故障
  faulty: {
    value: 'faulty',
    label: '故障',
    color: 'red',
  },
};

// 擅长的领域map
export const SpecialtyMap: Record<string, { label: string; value: string }> = {
  '1': {
    value: '1',
    label: '火灾扑救',
  },
  '2': {
    value: '2',
    label: '危化品泄漏处理',
  },
  '3': {
    value: '3',
    label: '设备抢修',
  },
  '4': {
    value: '4',
    label: '管线封堵',
  },
  '5': {
    value: '5',
    label: '受限空间救援',
  },
  '6': {
    value: '6',
    label: '高处救援',
  },
  '7': {
    value: '7',
    label: '医疗救援',
  },

  '0': {
    value: '0',
    label: '其他',
  },
};

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '救援队伍名称',
    },
    {
      fieldName: 'typeId',
      label: '队伍类型',
    },
    {
      fieldName: 'status',
      label: '队伍状态',
      component: 'Select',
      componentProps: {
        options: Object.values(TeamStatusMap),
      },
    },
    {
      fieldName: 'area',
      label: '所属厂区',
    },
    {
      fieldName: 'territory',
      label: '擅长领域',
      component: 'Select',
      componentProps: {
        options: Object.values(SpecialtyMap),
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CmTeamInfo> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    // 队伍编号
    {
      field: 'teamId',
      title: '队伍编号',
    },
    {
      field: 'teamName',
      title: '队伍名称',
    },
    {
      field: 'teamType',
      title: '队伍类型',
    },
    {
      field: 'factoryArea',
      title: '所属厂区',
    },
    // 负责人
    {
      field: 'leader',
      title: '负责人',
    },
    // 联系电话
    {
      field: 'contactPhone',
      title: '联系电话',
    },
    // 队员人数
    {
      field: 'totalMembers',
      title: '队员人数',
    },
    // 队伍状态
    {
      field: 'teamStatus',
      title: '队伍状态',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.teamStatus;
        return [TeamStatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },
    {
      field: 'action',
      title: '操作',
      width: 220,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    // 基础信息
    {
      component: 'Divider',
      fieldName: '',
      label: '基础信息',
    },
    {
      fieldName: 'teamName',
      label: '队伍名称',
      rules: 'required',
    },
    {
      fieldName: 'teamType',
      label: '队伍类型',
      rules: 'required',
    },
    {
      fieldName: 'factoryArea',
      label: '所属厂区',
      rules: 'required',
    },
    {
      fieldName: 'leader',
      label: '队伍负责人',
      rules: 'required',
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      rules: 'required',
    },
    {
      fieldName: 'email',
      label: '电子邮箱',
    },
    // 成立日期
    {
      fieldName: 'establishDate',
      label: '成立日期',
      component: 'DatePicker',
      rules: 'required',
    },
    // 队伍状态
    {
      fieldName: 'teamStatus',
      label: '队伍状态',
      component: 'Select',
      componentProps: {
        options: Object.values(TeamStatusMap),
      },
      rules: 'required',
    },

    // 人员配置
    {
      component: 'Divider',
      fieldName: '',
      label: '人员配置',
    },
    // 队员总数
    {
      fieldName: 'totalMembers',
      label: '队员总数',
      rules: 'required',
      component: 'InputNumber',
    },
    // 专职人员数
    {
      fieldName: 'fullTime',
      label: '专职人员数',
      component: 'InputNumber',
    },
    // 兼职人员数
    {
      fieldName: 'partTime',
      label: '兼职人员数',
      component: 'InputNumber',
    },
    // 技术人员数
    {
      fieldName: 'techStaff',
      label: '技术人员数',
      component: 'InputNumber',
    },
    // 专长领域
    {
      fieldName: 'specialtyName',
      label: '专长领域',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(SpecialtyMap),
      },
    },
    // 装备配置
    {
      component: 'Divider',
      fieldName: '',
      label: '装备配置',
    },
    {
      fieldName: 'equipmentInfoList',
      label: '消防设备',
      component: 'Add',
      custom: {
        addFormConfig: {
          wrapperClass: 'grid-cols-2',
        },
        addObj: [
          {
            fieldName: 'equipmentName',
            label: '设备名称',
            rules: 'required',
            formItemClass: 'col-span-2',
          },
          {
            fieldName: 'quantity',
            label: '数量',
            rules: 'required',
            component: 'InputNumber',
          },
          // 状态
          {
            fieldName: 'status',
            label: '状态',
            rules: 'required',
            component: 'Select',
            componentProps: {
              options: Object.values(EquipmentStatusMap),
            },
          },
        ],
      },
    },
    // 位置信息
    {
      component: 'Divider',
      fieldName: '',
      label: '位置信息',
    },
    // 详细地址
    {
      fieldName: 'address',
      label: '详细地址',
      rules: 'required',
      component: 'Textarea',
    },
    // 经度
    {
      fieldName: 'longitude',
      label: '经度',
      component: 'InputNumber',
    },
    // 纬度
    {
      fieldName: 'latitude',
      label: '纬度',
      component: 'InputNumber',
    },
    // 备注信息
    {
      component: 'Divider',
      fieldName: '',
      label: '备注信息',
    },
    // 队伍简介
    {
      fieldName: 'description',
      label: '队伍简介',
      component: 'Textarea',
    },
    // 备注
    {
      fieldName: 'remarks',
      label: '其他备注',
      component: 'Textarea',
    },
  ];
}
