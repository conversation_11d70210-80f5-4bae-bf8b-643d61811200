<!--
 * @Author: Strayer
 * @Date: 2025-07-29
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-04
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency-plan\manage\index.vue
-->
<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CmEmergencyPlans } from '#/api/core/emergency-plan/manage';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';
import type { StatisticItem } from '#/components/statistic-card.vue';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';
import { SvgCheckCircleIcon } from '@vben/icons';

import { PlusOutlined } from '@ant-design/icons-vue';
import { But<PERSON>, message } from 'ant-design-vue';

import {
  deleteEmergencyPlanApi,
  getEmergencyPlanCountApi,
  getEmergencyPlanDetailApi,
  getEmergencyPlanListApi,
  updateEmergencyPlanStatusApi,
} from '#/api/core/emergency-plan/manage';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';
import StatisticCard from '#/components/statistic-card.vue';

import { getColumnsScheme, getTopSearchScheme, StatusMap } from './data';
import Detail from './detail.vue';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');
const detailRef = useTemplateRef<ComponentExposed<typeof Detail>>('detailRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<CmEmergencyPlans>> =
  shallowRef(getColumnsScheme());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<CmEmergencyPlans>,
  fromData: any,
) {
  getCountData();

  return getEmergencyPlanListApi(param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: CmEmergencyPlans) {
  return deleteEmergencyPlanApi(row.planId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editHandle(row: CmEmergencyPlans) {
  gridFormRef.value?.gridApi.setLoading(true);
  getEmergencyPlanDetailApi(row.planId)
    .then((res) => {
      detailRef.value?.open(res);
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/** 新增 */
function addHandle() {
  detailRef.value?.open();
}

/** 下一步 */
function nextHandle(row: CmEmergencyPlans) {
  if (!StatusMap[row.status ?? '']?.next) return;

  updateEmergencyPlanStatusApi(
    row.planId,
    StatusMap[row.status ?? '']?.next ?? '',
  )
    .then(() => {
      message.success('更新成功');
      gridFormRef.value?.gridApi.query();
    })
    .catch(() => {
      message.error('更新失败');
    });
}

/** 驳回 */
function rejectHandle(row: CmEmergencyPlans) {
  if (row.status === '已驳回') return;

  updateEmergencyPlanStatusApi(row.planId, '已驳回')
    .then(() => {
      message.success('更新成功');
      gridFormRef.value?.gridApi.query();
    })
    .catch(() => {
      message.error('更新失败');
    });
}

const total = ref<StatisticItem[]>([]);

/** 获取统计数据 */
function getCountData() {
  total.value = [];
  getEmergencyPlanCountApi().then((res) => {
    for (const key of Object.keys(res.typeMap ?? {})) {
      total.value.push({
        title: key,
        value: res.typeMap[key] ?? 0,
        color: StatusMap[key]?.color ?? '#3bce87',
        svgComponent: StatusMap[key]?.component ?? SvgCheckCircleIcon,
      });
    }
  });
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <StatisticCard :total="total" />
      <!-- 表格 -->
      <GridForm
        ref="gridFormRef"
        title="应急预案列表"
        style="height: calc(100vh - 180px)"
        :columns-scheme="columnsScheme"
        :query="query"
        :top-search-scheme="topSearchScheme"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="addHandle"
          />
        </template>
        <template #action="{ row }">
          <Button
            v-if="StatusMap[row.status]?.next"
            type="link"
            @click="nextHandle(row)"
          >
            {{ StatusMap[row.status]?.nextLabel }}
          </Button>
          <Button
            v-if="row.status !== '已驳回'"
            type="text"
            @click="rejectHandle(row)"
          >
            驳回
          </Button>
          <Button type="link" @click="editHandle(row)"> 编辑 </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
    <Detail ref="detailRef" @submit-success="gridFormRef?.gridApi.reload()" />
  </Page>
</template>
