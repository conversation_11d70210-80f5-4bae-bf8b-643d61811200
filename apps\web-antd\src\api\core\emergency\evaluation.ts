import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * CmAccidentBasicInfo对象
 *
 * CmAccidentBasicInfo
 */
export type CmAccidentBasicInfo = {
  /**
   * 事故唯一标识
   */
  accidentId: string;
  /**
   * 事故严重程度等级
   */
  accidentLevel?: string;
  /**
   * 事故的具体名称
   */
  accidentName?: string;
  /**
   * 事故类型分类
   */
  accidentType?: string;
  /**
   * 附件信息
   */
  cmAccidentAttachmentsList?: CmAccidentAttachments[];
  /**
   * 事故报告
   */
  cmAccidentReports?: CmAccidentReports;
  /**
   * 报告编制人员
   */
  compiler?: string;
  /**
   * 报告编制的时间
   */
  compileTime?: string;
  /**
   * 事故发生的具体地点
   */
  occurLocation?: string;
  /**
   * 事故发生的具体时间
   */
  occurTime?: string;
  /**
   * 报告的类型
   */
  reportType?: string;
  /**
   * 报告的当前状态: 草稿/提交/审核中等
   */
  status?: string;
};

/**
 * CmAccidentAttachments对象
 *
 * CmAccidentAttachments
 */
export type CmAccidentAttachments = {
  /**
   * 关联的事故ID
   */
  accidentId: string;
  /**
   * 附件唯一标识
   */
  attachmentId?: string;
  /**
   * 附件的原始名称
   */
  attachmentName?: string;
  /**
   * 附件的类型 (图片 / 视频 / 文档)
   */
  attachmentType?: string;
  /**
   * 附件的大小 (字节)
   */
  fileSize?: number;
  /**
   * 附件在服务器的存储路径
   */
  storagePath?: string;
  /**
   * 附件上传人员
   */
  uploader?: string;
  /**
   * 附件上传时间
   */
  uploadTime?: string;
};

/**
 * 事故报告
 *
 * CmAccidentReports
 */
export type CmAccidentReports = {
  /**
   * 关联的事故ID
   */
  accidentId: string;
  /**
   * 事故发生的详细过程
   */
  accidentProcess?: string;
  /**
   * 报告附件的数量
   */
  attachmentNum?: number;
  /**
   * 事故原因分析
   */
  causeAnalysis?: string;
  /**
   * 针对事故的整改措施
   */
  rectification?: string;
  /**
   * 报告唯一标识
   */
  reportId?: string;
  /**
   * 事故责任认定情况
   */
  responsibility?: string;
  /**
   * 事故现场的勘察情况
   */
  siteSurvey?: string;
  /**
   * 报告的版本号
   */
  version?: string;
};

/** 获取评估列表 */
export function getEmergencyEvaluationListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: CmAccidentBasicInfo[];
    total: number;
  }>('/cm-accident-basic-info', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 获取评估详情 */
export function getEmergencyEvaluationDetailApi(accidentId: string) {
  return requestClient.get<CmAccidentBasicInfo>(
    `/cm-accident-basic-info/detail/${accidentId}`,
  );
}

/** 新增评估 */
export function addEmergencyEvaluationApi(data: CmAccidentBasicInfo) {
  return requestClient.post<CmAccidentBasicInfo>(
    '/cm-accident-basic-info',
    data,
  );
}

/** 修改评估 */
export function editEmergencyEvaluationApi(data: CmAccidentBasicInfo) {
  return requestClient.put<CmAccidentBasicInfo>(
    '/cm-accident-basic-info',
    data,
  );
}

/** 删除评估 */
export function deleteEmergencyEvaluationApi(ids: string[]) {
  return requestClient.delete<CmAccidentBasicInfo>(`/cm-accident-basic-info`, {
    data: { ids },
  });
}

/** 统计 */
export function getEmergencyEvaluationCountApi() {
  return requestClient.post<{
    totalCount: number;
    typeDateMap: Record<string, Record<string, number>>;
    typeMap: Record<string, number>;
  }>('/cm-accident-basic-info/statistics');
}
