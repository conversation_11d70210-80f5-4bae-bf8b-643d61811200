<script lang="ts" setup>
import type { ShallowRef } from 'vue';
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { CmDrillBasicInfo } from '#/api/core/emergency-drill/drill-evaluate';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addDrillEvaluateApi,
  deleteDrillEvaluateApi,
  editDrillEvaluateApi,
  getDrillEvaluateDetailApi,
  getDrillEvaluateListApi,
} from '#/api/core/emergency-drill/drill-evaluate';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<CmDrillBasicInfo>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<CmDrillBasicInfo>,
  fromData: any,
) {
  // 时间范围
  if (fromData.timeRange) {
    fromData.beginDate = fromData.timeRange[0].startOf('day').toISOString();
    fromData.endDate = fromData.timeRange[1].endOf('day').toISOString();
    delete fromData.timeRange;
  }

  return getDrillEvaluateListApi(true, param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: CmDrillBasicInfo) {
  return deleteDrillEvaluateApi(row.drillId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: CmDrillBasicInfo) {
  gridFormRef.value?.gridApi.setLoading(true);
  getDrillEvaluateDetailApi(row.drillId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '编辑演练记录',
        formData: {
          ...res,
          cmDrillAttachments: res.cmDrillAttachments?.path,
        },
        meta: { type: 'edit', raw: row },
      });
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: CmDrillBasicInfo;
}): Promise<boolean> {
  param.formData.cmDrillAttachments = {
    path: param.formData.cmDrillAttachments,
    drillId: param.meta.raw?.drillId,
  };

  if (param.meta.type === 'create')
    addDrillEvaluateApi({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editDrillEvaluateApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="演练记录列表"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
        :form-config="{
          wrapperClass: 'grid-cols-2',
        }"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增演练记录',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
