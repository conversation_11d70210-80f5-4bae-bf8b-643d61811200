<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type {
  PriceCountChartDTO,
  PriceCountDTO,
} from '#/api/core/energy-management/consume-statistic';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { onMounted, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';
import * as echarts from 'echarts';

import { getAreaListApi } from '#/api/core/energy-management/area-manage';
import { getConsumeStatisticListApi } from '#/api/core/energy-management/consume-statistic';
import GridForm from '#/components/grid-form/index.vue';
import TableTopSearch from '#/components/tableTopSearch.vue';

import { getColumnsScheme, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(false);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<PriceCountDTO>> =
  shallowRef(getColumnsScheme());
const tableTopSearchRef =
  useTemplateRef<ComponentExposed<typeof TableTopSearch>>('tableTopSearchRef');

const searchFormData = ref<Record<string, any>>({});

const initSearchFormData = ref({
  timeRange: [dayjs().startOf('day'), dayjs().endOf('day')],
  water: 1, // 默认水
  areaId: '',
});

// 获取区域数据
onMounted(async () => {
  const areaData = await getAreaListApi(
    {
      currentPage: 1,
      pageSize: 999_999,
    },
    {},
  );

  initSearchFormData.value.areaId = areaData.items?.[0]?.areaId ?? '';
  searchFormData.value = initSearchFormData.value;
  hadLoad.value = true;
});

/**
 * @description: 获取表格数据
 */
async function query() {
  const fromData = {
    ...searchFormData.value,
  };
  fromData.startTime = (
    fromData.timeRange?.[0] ?? dayjs().startOf('day')
  ).toISOString();
  fromData.endTime = (
    fromData.timeRange?.[1] ?? dayjs().endOf('day')
  ).toISOString();
  delete fromData.timeRange;

  if (!fromData.water) {
    fromData.water = 1;
  }

  const res = await getConsumeStatisticListApi(fromData);

  renderEcharts({
    startTime: fromData.startTime,
    endTime: fromData.endTime,
    res: res.priceCountChartList ?? [],
    type: fromData.water,
  });

  return {
    total: res.priceCountList?.length ?? 0,
    items:
      (res.priceCountList?.map((item) => ({
        ...item,
        price: item.price + (fromData.water === 1 ? 'm³' : 'KWH'),
      })) as any) ?? [],
  };
}

function search(value: Record<string, any>) {
  searchFormData.value = value;
  gridFormRef.value?.gridApi.reload();
}

let myChart: any;
/** 渲染图表 */
function renderEcharts(param: {
  endTime: string;
  res: PriceCountChartDTO[];
  startTime: string;
  type: number;
}) {
  if (!myChart)
    myChart = echarts.init(
      document.querySelector(
        '#energy-management-consume-statistic',
      ) as HTMLElement,
    );

  const xAxisData: string[] = [];
  const seriesDataMap: Record<string, (number | undefined)[]> = {
    usage: [],
    price: [],
  };

  const legendDataArr = [`用量`, '费用'];

  // 如果开始时间和结束时间之间不超过24小时，则显示'HH:mm'，否则显示'YYYY-MM-DD HH:mm'
  const diffHours = dayjs(param.endTime).diff(dayjs(param.startTime), 'hour');

  const formatStr = Math.abs(diffHours) <= 24 ? 'HH:mm' : 'YYYY-MM-DD HH:mm';

  for (const item of param.res) {
    xAxisData.push(item.date ? dayjs(item.date).format(formatStr) : '');
    seriesDataMap.usage?.push(item.usage);
    // seriesDataMap.price?.push(item.price);
  }

  // 绘制图表
  myChart.setOption({
    title: {
      text: '能耗趋势图',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
      },
      top: 4,
      let: 22,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    legend: {
      data: legendDataArr,
    },
    xAxis: {
      data: xAxisData,
    },
    yAxis: [
      {
        type: 'value',
        name: `用量(${param.type === 1 ? 'm³' : 'KWH'})`,
        position: 'left',
        alignTicks: true,
        axisLine: {
          show: true,
        },
      },
      // {
      //   type: 'value',
      //   name: '费用(元)',
      //   position: 'right',
      //   alignTicks: true,
      //   axisLine: {
      //     show: true,
      //   },
      // },
    ],
    series: [
      {
        name: '用量',
        type: 'line',
        smooth: true,
        data: seriesDataMap.usage,
      },
      // {
      //   name: '费用',
      //   type: 'bar',
      //   yAxisIndex: 1,
      //   data: seriesDataMap.price,
      // },
    ],
  });
}
</script>

<template>
  <Page auto-content-height class="flex flex-col">
    <template v-if="hadLoad">
      <!-- 顶部搜索 -->
      <TableTopSearch
        ref="tableTopSearchRef"
        :schema="topSearchScheme"
        :ini-form="initSearchFormData"
        @search="search"
      />
      <div
        class="flex justify-between gap-4"
        style="height: calc(var(--vben-content-height) - 200px)"
      >
        <GridForm
          style="width: calc(50% - 8px)"
          ref="gridFormRef"
          title="能耗统计"
          :columns-scheme="columnsScheme"
          :query="query"
        />
        <!-- 图表 -->
        <div
          id="energy-management-consume-statistic"
          class="h-[60vh] bg-white p-1"
          style="width: calc(50% - 8px)"
        ></div>
      </div>
    </template>
  </Page>
</template>
