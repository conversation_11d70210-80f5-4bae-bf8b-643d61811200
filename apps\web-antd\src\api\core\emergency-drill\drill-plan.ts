import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * CmDrillPlan
 */
export type CmDrillPlan = {
  /**
   * 应急预案id
   */
  cmplanId: string;
  /**
   * 记录创建时间（默认当前时间戳）
   */
  createdAt?: string;
  /**
   * 演练的详细描述
   */
  description?: string;
  /**
   * 目标与要求
   */
  detail?: string;
  /**
   * 演练步骤
   */
  drillSteps?: CmDrillSteps[];
  /**
   * 演练的具体时间
   */
  drillTime?: string;
  /**
   * 演练类型（综合、专项等）
   */
  drillType?: string;
  /**
   * 时长
   */
  duration?: string;
  /**
   * 演练后的评估报告
   */
  evaluationReport?: string;
  /**
   * 等级
   */
  level?: string;
  /**
   * 地点
   */
  location?: string;
  /**
   * 演练的预期目标
   */
  objectives?: string;
  /**
   * 历史id
   */
  oldId?: string;
  /**
   * 组织演练的单位
   */
  organizer?: string;
  /**
   * 演练计划的唯一标识符（主键）
   */
  planId: number;
  /**
   * 演练的最终评估结果
   */
  result?: string;
  /**
   * 演练的当前状态（默认：计划中）
   */
  status?: string;
  /**
   * 主题
   */
  subject?: string;
  /**
   * 演练计划标题
   */
  title?: string;
  /**
   * 记录更新时间（自动更新）
   */
  updatedAt?: string;
};

/**
 * CmDrillSteps对象
 *
 * CmDrillSteps
 */
export type CmDrillSteps = {
  /**
   * 步骤的实际完成情况
   */
  actualOutcome?: string;
  /**
   * 部门
   */
  department?: string;
  /**
   * 步骤的详细描述
   */
  description?: string;
  /**
   * 时长
   */
  duration?: string;
  /**
   * 步骤的预期完成效果
   */
  expectedOutcome?: string;
  /**
   * 关联的演练计划ID
   */
  planId: number;
  /**
   * 唯一标识演练步骤
   */
  stepId: number;
  /**
   * 步骤在演练中的顺序
   */
  stepOrder?: number;
  /**
   * 步骤的标题
   */
  title?: string;
};

/** 获取演练计划列表 */
export async function getDrillPlanListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: CmDrillPlan[];
    total: number;
  }>('/cm-drill-plan', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 获取演练计划详情 */
export async function getDrillPlanDetailApi(planId: number) {
  return requestClient.get<CmDrillPlan>(`/cm-drill-plan/detail/${planId}`);
}

/** 新增演练计划 */
export async function addDrillPlanApi(data: CmDrillPlan) {
  return requestClient.post<CmDrillPlan>('/cm-drill-plan', data);
}

/** 修改演练计划 */
export async function editDrillPlanApi(data: CmDrillPlan) {
  return requestClient.put<CmDrillPlan>('/cm-drill-plan', data);
}

/** 删除演练计划 */
export async function deleteDrillPlanApi(planId: number) {
  return requestClient.delete<CmDrillPlan>(`/cm-drill-plan/${planId}`);
}
