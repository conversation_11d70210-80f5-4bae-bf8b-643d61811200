<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { EnPeriodType } from '#/api/core/energy-management/period-type';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addPeriodTypeApi,
  deletePeriodTypeApi,
  editPeriodTypeApi,
  getPeriodTypeListApi,
} from '#/api/core/energy-management/period-type';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
// const topSearchScheme: ShallowRef<CustomFormSchema[]> =
//   shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<EnPeriodType>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  _param: VxeGridPropTypes.ProxyAjaxQueryParams<EnPeriodType>,
  fromData: any,
) {
  const res = await getPeriodTypeListApi(fromData);
  return {
    items: res.sort((a, b) => a.sortOrder - b.sortOrder),
    total: res.length,
  };
}

/**
 * @description: 删除
 */
function deletePeriodType(row: EnPeriodType) {
  return deletePeriodTypeApi(row.periodTypeId!).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  console.log('🚀 ~ onSubmit ~ param.formData:', param.formData);
  return param.meta.type === 'create'
    ? addPeriodTypeApi(param.formData)
        .then(() => {
          message.success('新增成功');
          gridFormRef.value?.gridApi.reload();
          return true;
        })
        .catch(() => false)
    : editPeriodTypeApi({
        ...param.raw,
        ...param.formData,
      })
        .then(() => {
          message.success('更新成功');
          gridFormRef.value?.gridApi.query();
          return true;
        })
        .catch(() => false);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :disable-page="true"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '时段类型信息',
                meta: { type: 'create' },
              })
            "
          />
        </template>
        <template #action="{ row }">
          <Button
            type="link"
            @click="
              gridFormRef?.formModalRef?.open({
                title: '时段类型信息',
                formData: row,
                meta: { type: 'edit' },
              })
            "
          >
            编辑
          </Button>
          <RemoveButton @confirm="deletePeriodType(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
