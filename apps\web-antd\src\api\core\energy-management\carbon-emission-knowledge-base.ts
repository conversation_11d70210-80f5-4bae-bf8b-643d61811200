/*
 * @Author: Strayer
 * @Date: 2025-08-20
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-20
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\api\core\energy-management\carbon-emission-knowledge-base.ts
 */

import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * 知识库对象
 */
export type EnCarbonEmissionKnowledge = {
  /**
   * 创建人
   */
  createName?: string;
  /**
   * 创建时间（默认当前时间戳）
   */
  createTime?: string;
  /**
   * 主键ID（自动递增）
   */
  knowledgeId: number;
  /**
   * 知识条目名称
   */
  nodeName?: string;
  /**
   * 最后更新人
   */
  updateName?: string;
  /**
   * 最后更新时间（自动更新）
   */
  updateTime?: string;
};

/**
 * 知识库文件
 */
export type EnCarbonEmissionKnowledgeFile = {
  /**
   * 文件存储路径
   */
  filePath: string;
  /**
   * 主键ID（自动递增）
   */
  id: number;
  /**
   * 知识库主键
   */
  knowledgeId: number;
};

/**
 * 知识库列表
 */
export async function getCarbonEmissionKnowledgeListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: EnCarbonEmissionKnowledge[];
    total: number;
  }>('/carbon/knowledge/page', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/**
 * 新增知识库
 */
export async function addCarbonEmissionKnowledgeApi(
  data: EnCarbonEmissionKnowledge,
) {
  return requestClient.post<EnCarbonEmissionKnowledge>(
    '/carbon/knowledge',
    data,
  );
}

/**
 * 修改知识库
 */
export async function editCarbonEmissionKnowledgeApi(
  data: EnCarbonEmissionKnowledge,
) {
  return requestClient.put<EnCarbonEmissionKnowledge>(
    '/carbon/knowledge',
    data,
  );
}

/**
 * 删除知识库
 */
export async function deleteCarbonEmissionKnowledgeApi(knowledgeId: number) {
  return requestClient.delete<string>(`/carbon/knowledge/${knowledgeId}`);
}

/**
 * 获取知识库详文件列表
 */
export async function getCarbonEmissionKnowledgeFileListApi(
  knowledgeId: number,
) {
  return requestClient.get<EnCarbonEmissionKnowledgeFile[]>(
    `/carbon/knowledgeFile/list`,
    {
      params: {
        knowledgeId,
      },
    },
  );
}

/**
 * 新增知识库文件
 */
export async function addCarbonEmissionKnowledgeFileApi(
  data: EnCarbonEmissionKnowledgeFile,
) {
  return requestClient.post<EnCarbonEmissionKnowledgeFile>(
    '/carbon/knowledgeFile',
    data,
  );
}

/**
 * 删除知识库文件
 */
export async function deleteCarbonEmissionKnowledgeFileApi(id: number) {
  return requestClient.delete<string>(`/carbon/knowledgeFile/${id}`);
}
