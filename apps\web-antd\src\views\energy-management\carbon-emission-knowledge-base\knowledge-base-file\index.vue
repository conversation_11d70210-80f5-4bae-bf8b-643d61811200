<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { EnCarbonEmissionKnowledgeFile } from '#/api/core/energy-management/carbon-emission-knowledge-base';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addCarbonEmissionKnowledgeFileApi,
  deleteCarbonEmissionKnowledgeFileApi,
  getCarbonEmissionKnowledgeFileListApi,
} from '#/api/core/energy-management/carbon-emission-knowledge-base';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';
import { downloadFile } from '#/utils/comm';

import { getColumnsScheme, getFormOption } from './data';

const props = defineProps<{ knowledgeId: string }>();

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const columnsScheme: ShallowRef<
  CustomColumnSchema<EnCarbonEmissionKnowledgeFile>
> = shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query() {
  return getCarbonEmissionKnowledgeFileListApi(Number(props.knowledgeId)).then(
    (res) => ({
      items: res,
      total: res.length,
    }),
  );
}

/**
 * @description: 删除
 */
function deletePersonnel(row: EnCarbonEmissionKnowledgeFile) {
  return deleteCarbonEmissionKnowledgeFileApi(row.id).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: EnCarbonEmissionKnowledgeFile;
}): Promise<boolean> {
  if (param.meta.type === 'create') {
    addCarbonEmissionKnowledgeFileApi({
      ...param.formData,
      knowledgeId: props.knowledgeId,
    })
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  }

  return Promise.resolve(true);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="知识库文件列表"
        back-route="/energy-management/carbon-emission-knowledge-base"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :submit="onSubmit"
        :disable-page="true"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '新增知识库文件',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <!-- 下载 -->
          <Button
            type="link"
            @click="
              downloadFile(
                row.filePath,
                row.filePath.split('/').pop() ?? row.id,
              )
            "
          >
            下载
          </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
