<script lang="ts" setup>
import type { Dayjs } from 'dayjs';

import type { VxeGridProps } from '@vben/plugins/vxe-table';

import { onMounted, onUnmounted, shallowRef } from 'vue';

import dayjs from 'dayjs';
import * as echarts from 'echarts';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { listAreaApi } from '#/api/core/energy-management/area-manage';
import {
  getComparisonAnalysisListApi,
  getTrendBarChartDataApi,
  getTrendLineChartDataApi,
} from '#/api/core/energy-management/energy-coal-comparison-analysis';

const areaList = shallowRef<{ label?: string; value: string }[]>([]);
listAreaApi().then((res) => {
  areaList.value = res.map((e) => ({
    label: e.areaName,
    value: e.areaId,
  }));
});

const [QueryForm] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        filterOption: true,
        options: areaList,
        placeholder: '请选择',
        showSearch: true,
      },
      fieldName: 'areaId',
      label: '区域',
      labelWidth: 70,
    },

    {
      component: 'RangePicker',
      componentProps: {},
      fieldName: 'dateRange',
      label: '日期选择框',
    },
  ],
  submitButtonOptions: {
    content: '查询',
  },
  wrapperClass: 'grid-cols-3 ',
});

const searchParams = shallowRef<any>({});
function onSubmit(values: { areaId: string; dateRange: [Dayjs, Dayjs] }) {
  searchParams.value = values;
  const { areaId, dateRange } = values;
  const params = {
    areaId,
    startTime: dateRange[0].startOf('day').toISOString(),
    endTime: dateRange[1].endOf('day').toISOString(),
  };

  searchParams.value = { ...params };

  initLineChart(params);

  gridApi.query();
}

onMounted(() => {
  initBarChart();
});

async function initBarChart() {
  const res = await getTrendBarChartDataApi();

  const barChart = echarts.init(
    document.querySelector(
      '#energy-coal-comparison-analysis-bar-chart',
    ) as HTMLElement,
  );

  barChart.setOption({
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: res.map((e) => e.dateString),
    },
    yAxis: {
      type: 'value',
    },
    grid: {
      left: '4%',
      right: '4%',
      bottom: '4%',
      containLabel: true,
    },
    legend: {
      right: 10,
      top: 10,
      data: ['水', '电', '标煤'],
      selectedMode: 'single',
    },
    series: [
      {
        name: '水',
        type: 'bar',
        data: res.map((e) => e.waterUsage),
      },
      {
        name: '电',
        type: 'bar',
        data: res.map((e) => e.electricityUsage),
      },
      {
        name: '标煤',
        type: 'bar',
        data: res.map((e) => e.factorUsage),
      },
    ],
  });

  onUnmounted(() => {
    barChart.dispose();
  });
}

const lineChart = shallowRef<echarts.ECharts | null>(null);
async function initLineChart(params: Record<string, any>) {
  if (lineChart.value) {
    lineChart.value.dispose();
    lineChart.value = null;
  }

  const res = await getTrendLineChartDataApi(params);

  lineChart.value = echarts.init(
    document.querySelector(
      '#energy-coal-comparison-analysis-line-chart',
    ) as HTMLElement,
  );

  lineChart.value.setOption({
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: res.map((e) => e.dateString),
    },
    yAxis: {
      type: 'value',
    },
    grid: {
      left: '4%',
      right: '4%',
      bottom: '4%',
      containLabel: true,
    },
    legend: {
      right: 10,
      top: 10,
      data: ['水', '电', '标煤'],
      selectedMode: 'single',
    },
    series: [
      {
        name: '水',
        type: 'line',
        data: res.map((e) => e.waterUsage),
      },
      {
        name: '电',
        type: 'line',
        data: res.map((e) => e.electricityUsage),
      },
      {
        name: '标煤',
        type: 'line',
        data: res.map((e) => e.factorUsage),
      },
    ],
  });

  onUnmounted(() => {
    lineChart.value?.dispose();
    lineChart.value = null;
  });
}

const gridOptions: VxeGridProps = {
  columns: [
    {
      title: '时间',
      field: 'recordDate',
      formatter: (row: any) => dayjs(row.recordDate).format('YYYY-MM-DD'),
    },
    { title: '用能区域', field: 'areaName' },
    { field: 'waterUsage', title: '用水(吨)' },
    { field: 'electricityUsage', title: '用电(度)' },
    { field: 'factorUsage', title: '标煤(吨)' },
    { field: 'emissionUsage', title: '碳排放(吨)' },
    { field: 'factorUsageRatio', title: '同比变化' },
  ],
  pagerConfig: {
    pageSize: 5,
    pageSizes: [5, 10, 15, 20],
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        if (!searchParams.value.startTime || !searchParams.value.endTime) {
          return {
            total: 0,
            items: [],
          };
        }

        const res = await getComparisonAnalysisListApi({
          pageNumber: page.currentPage,
          pageSize: page.pageSize,
          ...searchParams.value,
        });

        return {
          total: res.total,
          items: res.data,
        };
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
</script>
<template>
  <div class="p-4">
    <div class="mb-4 bg-white px-6 pt-6">
      <QueryForm />
    </div>

    <div class="mb-4 flex gap-4">
      <div
        id="energy-coal-comparison-analysis-bar-chart"
        class="h-[400px] flex-1 bg-white"
      ></div>
      <div
        id="energy-coal-comparison-analysis-line-chart"
        class="h-[400px] flex-1 bg-white"
      ></div>
    </div>

    <div class="bg-white">
      <Grid />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
