import { requestClient } from '#/api/request';

/**
 * ElectricityUsageRecordListDTO
 */
export type ElectricityUsageRecordListDTO = {
  /**
   * 抬头
   */
  electricityList?: ElectricityDTO[];
  /**
   * 用电计量分页列表
   */
  electricityUsageRecordList?: PageElectricityUsageRecordDTO;
};

/**
 * 用电计量抬头
 *
 * ElectricityDTO
 */
export type ElectricityDTO = {
  /**
   * 区域唯一标识符
   */
  areaId: string;
  /**
   * 区域名称：总用电量名称 为 总用电量
   */
  areaName?: string;
  /**
   * 用电量（单位:m³）
   */
  electricityUsage?: number;
  /**
   * 同上周同一天对比百分比
   */
  electricityUsageRatio?: number;
  /**
   * true 为上升， false 为下降
   */
  upOrDown?: boolean;
};

/**
 * 用电计量分页列表
 *
 * PageElectricityUsageRecordDTO
 */
export type PageElectricityUsageRecordDTO = {
  /**
   * 查询数据列表
   */
  records?: ElectricityUsageRecordDTO[];
  /**
   * 总数
   */
  total?: number;
};

/**
 * 用水计量
 *
 * ElectricityUsageRecordDTO
 */
export type ElectricityUsageRecordDTO = {
  /**
   * 区域编码
   */
  areaCode?: string;
  /**
   * 区域ID
   */
  areaId?: string;
  /**
   * 区域名称
   */
  areaName?: string;
  /**
   * 创建时间戳（自动记录）
   */
  createTime?: string;
  /**
   * 设备编号
   */
  deviceCode?: string;
  /**
   * 设备ID
   */
  deviceId?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 用电量（单位:kWh）
   */
  electricityUsage?: number;
  /**
   * 记录结束时间（精确到小时）
   */
  endTime?: string;
  /**
   * 记录日期
   */
  recordDate?: string;
  /**
   * 记录唯一标识符
   */
  recordId: string;
  /**
   * 记录开始时间（精确到小时）
   */
  startTime?: string;
  /**
   * 更新时间戳（自动更新）
   */
  updateTime?: string;
};

/** 获取用电统计列表 */
export async function getElectricStatisticListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClient.get<ElectricityUsageRecordListDTO>(
    '/energyConsumptionManagement/electricityUsageRecord/page',
    {
      params: {
        pageNumber: param.currentPage,
        pageSize: param.pageSize,
        ...search,
      },
    },
  );
}

/** 新增用电统计 */
export async function addElectricStatisticApi(data: ElectricityUsageRecordDTO) {
  return requestClient.post<ElectricityUsageRecordDTO>(
    '/energyConsumptionManagement/electricityUsageRecord',
    data,
  );
}

/** 修改用电统计 */
export async function editElectricStatisticApi(
  data: ElectricityUsageRecordDTO,
) {
  return requestClient.put<ElectricityUsageRecordDTO>(
    '/energyConsumptionManagement/electricityUsageRecord',
    data,
  );
}

/** 删除用电统计 */
export async function deleteElectricStatisticApi(recordId: string) {
  return requestClient.delete<string>(
    `/energyConsumptionManagement/electricityUsageRecord/${recordId}`,
  );
}
