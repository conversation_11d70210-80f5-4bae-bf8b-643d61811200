<!--
 * @Author: Strayer
 * @Date: 2025-07-29
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-31
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency-plan\manage\detail.vue
-->
<script lang="ts" setup>
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { CmEmergencyPlans } from '#/api/core/emergency-plan/manage';

import { nextTick, ref, useTemplateRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Card, message } from 'ant-design-vue';

import {
  addEmergencyPlanApi,
  editEmergencyPlanApi,
} from '#/api/core/emergency-plan/manage';
import FormBase from '#/components/form-modal/formBase.vue';

import { getFormOption } from './data';
import Flowsheet from './flowsheet/index.vue';

const emit = defineEmits<{
  (e: 'submitSuccess'): void;
}>();

// 标题，也可以不传，调用open方法时再传入也行
const title = defineModel<string>('title', { default: '' });

const formOption = getFormOption();

// 用户和内部的wangEditor全屏同步
const wangEditorFullscreen = ref(false);

const formBaseRef =
  useTemplateRef<ComponentExposed<typeof FormBase>>('formBaseRef');
const flowsheetRef =
  useTemplateRef<ComponentExposed<typeof Flowsheet>>('flowsheetRef');

const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formBaseRef?.value?.launchSubmit();
  },
});

/** 表单提交 */
function submitHandle(param: { formData: any; meta: any; raw: any }) {
  modalApi.lock();
  param.formData.flow = JSON.stringify(flowsheetRef?.value?.exportData());

  if (param.meta.type === 'create')
    addEmergencyPlanApi({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        emit('submitSuccess');
        modalApi.close();
      })
      .finally(() => {
        modalApi.unlock();
      });
  else if (param.meta.type === 'edit')
    editEmergencyPlanApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        emit('submitSuccess');
        modalApi.close();
      })
      .finally(() => {
        modalApi.unlock();
      });
}

// 提供给外部的打开弹窗方法
function open(row?: CmEmergencyPlans) {
  title.value = row ? '编辑应急预案' : '新增应急预案';

  modalApi.open();

  nextTick(() => {
    formBaseRef?.value?.setData({
      formData: row,
      meta: { type: row ? 'edit' : 'create', raw: row },
    });

    flowsheetRef?.value?.setData(row?.flow ? JSON.parse(row.flow) : []);
  });
}

defineExpose({
  formApi: formBaseRef?.value?.formApi,
  modalApi,
  open,
});
</script>

<template>
  <Modal
    class="w-[90%] bg-[#f6f8f9]"
    :title="title ?? ''"
    title-tooltip="1111"
    description="2222"
    :fullscreen-button="!wangEditorFullscreen"
    :closable="!wangEditorFullscreen"
    :footer="!wangEditorFullscreen"
  >
    <Card title="预案基本信息" size="small">
      <FormBase
        ref="formBaseRef"
        :form-option="formOption"
        :submit="submitHandle"
        :modal-api="modalApi"
        v-model:wang-editor-fullscreen="wangEditorFullscreen"
        :form-config="{
          wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        }"
      />
    </Card>
    <Card class="mt-3" title="应急预案流程图" size="small">
      <div class="absolute right-3 top-3 z-10 text-xs">
        提示：按住ctrl不放可以拖拽或缩放画布
      </div>
      <Flowsheet ref="flowsheetRef" class="h-[600px] w-full" />
    </Card>
  </Modal>
</template>
