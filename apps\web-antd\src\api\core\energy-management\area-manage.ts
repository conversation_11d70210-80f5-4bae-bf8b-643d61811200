import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * EnArea对象
 *
 * EnArea
 */
export type EnArea = {
  /**
   * 区域编码
   */
  areaCode?: string;
  /**
   * 区域唯一标识符
   */
  areaId: string;
  /**
   * 区域名称
   */
  areaName?: string;
  /**
   * 创建时间戳
   */
  createTime?: string;
  /**
   * 区域备注信息
   */
  remark?: string;
  /**
   * 更新时间戳（自动更新）
   */
  updateTime?: string;
};

/**
 * 获取区域列表
 */
export function getAreaListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: EnArea[];
    total: number;
  }>('/energyConsumptionManagement/area/page', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/**
 * 新增区域
 */
export async function addAreaApi(data: EnArea) {
  return requestClient.post<EnArea>('/energyConsumptionManagement/area', data);
}

/**
 * 修改区域
 */
export async function editAreaApi(data: EnArea) {
  return requestClient.put<EnArea>('/energyConsumptionManagement/area', data);
}

/**
 * 删除区域
 */
export async function deleteAreaApi(areaId: string) {
  return requestClient.delete<string>(
    `/energyConsumptionManagement/area/${areaId}`,
  );
}

/**
 * 列出所有区域
 */
export async function listAreaApi(): Promise<EnArea[]> {
  return requestClient.get('/energyConsumptionManagement/area/list');
}
