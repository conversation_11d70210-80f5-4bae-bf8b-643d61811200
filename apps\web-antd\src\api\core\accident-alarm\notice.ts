import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * EventNotification
 */
export type EventNotification = {
  /**
   * 对事件的详细描述
   */
  eventDescription?: string;
  /**
   * 事件的等级（如特别重大、重大等）
   */
  eventLevel?: string;
  /**
   * 事件发生的地点
   */
  eventLocation?: string;
  /**
   * 事件的标题
   */
  eventTitle?: string;
  /**
   * 事件类型（如自然灾害、事故灾难等）
   */
  eventType?: string;
  /**
   * 事件的影响范围
   */
  impactScope?: string;
  /**
   * 已经采取的应对措施
   */
  measuresTaken?: string;
  /**
   * 事件通知唯一标识符
   */
  notificationId: string;
  /**
   * 通知人列表
   */
  notificationRecipients?: CmNotificationRecipient[];
  /**
   * 通知的发送状态（默认未发送）
   */
  notificationStatus?: string;
  /**
   * 事件发生的时间
   */
  occurrenceTime?: string;
  /**
   * 事件的初步原因分析
   */
  preliminaryCause?: string;
  /**
   * 事件的优先级（紧急/高优先级/中优先级/低优先级）
   */
  priority?: string;
  /**
   * 事件的处理状态（默认待处理）
   */
  processingStatus?: string;
  /**
   * 上报人的部门
   */
  reporterDepartment?: string;
  /**
   * 上报人的电子邮箱
   */
  reporterEmail?: string;
  /**
   * 事件的上报人姓名
   */
  reporterName?: string;
  /**
   * 上报人的联系电话
   */
  reporterPhone?: string;
  /**
   * 事件的上报时间（自动记录）
   */
  reportTime?: string;
};

/**
 * CmNotificationRecipient对象
 *
 * CmNotificationRecipient
 */
export type CmNotificationRecipient = {
  /**
   * 关联的事件通知ID（外键）
   */
  notificationId: string;
  /**
   * 接收人是否阅读通知（未阅读/已阅读）
   */
  readStatus?: string;
  /**
   * 接收人阅读通知的时间
   */
  readTime?: string;
  /**
   * 接收人的电子邮箱
   */
  receiverEmail?: string;
  /**
   * 接收人的唯一标识符（主键）
   */
  receiverId: number;
  /**
   * 接收人的姓名
   */
  receiverName?: string;
  /**
   * 接收人的联系电话
   */
  receiverPhone?: string;
  /**
   * 接收人角色（如领导、专家、应急人员等）
   */
  receiverRole?: string;
  /**
   * 通知的发送状态（待发送/已发送/发送失败）
   */
  sendStatus?: string;
  /**
   * 通知的发送时间
   */
  sendTime?: string;
};

/** 获取事件通知列表 */
export function getEventNoticeListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: EventNotification[];
    total: number;
  }>('/event-notification', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 获取事件通知详情 */
export async function getEventNoticeDetailApi(notificationId: string) {
  return requestClient.get<EventNotification>(
    `/event-notification/detail/${notificationId}`,
  );
}

/** 新增事件通知 */
export async function addEventNoticeApi(data: EventNotification) {
  return requestClient.post<EventNotification>('/event-notification', data);
}

/** 修改事件通知 */
export async function editEventNoticeApi(data: EventNotification) {
  return requestClient.put<EventNotification>('/event-notification', data);
}

/** 删除事件通知 */
export async function deleteEventNoticeApi(notificationId: string) {
  return requestClient.delete<EventNotification>(
    `/event-notification/${notificationId}`,
  );
}
