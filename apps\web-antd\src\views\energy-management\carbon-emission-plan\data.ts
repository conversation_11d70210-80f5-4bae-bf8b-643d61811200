import type { CarbonEmissionPlan } from '#/api/core/energy-management/carbon-emission-plan';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'date',
      label: '年份',
      component: 'DatePicker',
      componentProps: {
        picker: 'year',
        allowClear: false,
        valueFormat: 'YYYY',
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CarbonEmissionPlan> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'planId', title: 'ID', visible: false },
    {
      field: 'year',
      title: '年份',
    },
    { field: 'targetEmission', title: '目标排放量' },
    { field: 'actualEmission', title: '实际排放量' },
    { field: 'createdTime', title: '创建时间', visible: false },
    { field: 'updatedTime', title: '更新时间', visible: false },
    {
      field: 'action',
      title: '操作',
      width: 200,
    },
  ];
}
