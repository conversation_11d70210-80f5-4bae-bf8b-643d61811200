import type { EnDevice } from '#/api/core/energy-management/device-manage';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 碳排放模板接口类型
export interface EnCarbonEmissionTemplate {
  templateId?: number;
  templateName: string;
  templateData: string;
  status: number;
  createdTime?: string;
  updatedTime?: string;
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EnCarbonEmissionTemplate> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'templateId', title: 'ID', visible: false },
    // 模板名称
    { field: 'templateName', title: '模板名称' },
    // 状态
    {
      field: 'status',
      title: '状态',
      formatter: ({ cellValue }) => {
        if (cellValue === 1) {
          return '启用';
        }
        if (cellValue === 0) {
          return '不启用';
        }
        return '';
      },
    },
    // 创建时间
    {
      field: 'createdTime',
      title: '创建时间',
      customType: 'date',
      visible: false,
    },
    // 更新时间
    {
      field: 'updatedTime',
      title: '更新时间',
      customType: 'date',
      visible: false,
    },
    {
      field: 'action',
      title: '操作',
      width: 200,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(deviceList: EnDevice[]): CustomFormSchema[] {
  return [
    {
      fieldName: 'templateName',
      label: '模板名称',
      rules: 'required',
      component: 'Input',
      componentProps: {
        placeholder: '请输入模板名称',
      },
    },
    {
      fieldName: 'templateData',
      label: '模板数据',
      component: 'Add',
      custom: {
        addFormConfig: {
          wrapperClass: 'grid-cols-3',
        },
        addObj: [
          {
            fieldName: 'deviceId',
            label: '节点名称',
            rules: 'required',
            component: 'Select',
            labelWidth: 80,
            componentProps: {
              placeholder: '请选择设备',
              options: deviceList.map((item) => ({
                label: item.deviceName,
                value: item.deviceId,
              })),
            },
          },
          {
            fieldName: 'nodeData',
            label: '节点指标',
            labelWidth: 80,
            rules: 'required',
            component: 'InputNumber',
          },
          {
            fieldName: 'metrics',
            label: '节点维度',
            labelWidth: 80,
            rules: 'required',
            component: 'Select',
            componentProps: {
              options: [
                { label: '用水', value: 1 },
                { label: '用电', value: 2 },
              ],
            },
          },
        ],
      },
    },
    // {
    //   fieldName: 'status',
    //   label: '状态',
    //   rules: 'required',
    //   component: 'Select',
    //   componentProps: {
    //     options: [
    //       { label: '启用', value: 1 },
    //       { label: '不启用', value: 0 },
    //     ],
    //   },
    // },
  ];
}
