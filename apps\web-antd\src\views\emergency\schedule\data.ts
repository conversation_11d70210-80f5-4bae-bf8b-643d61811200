import type { Dayjs } from 'dayjs';

/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-25
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\schedule\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import { getOrganizationPersonListApi } from '#/api/core/emergency/organization';

export const shiftMap: Record<
  string,
  { color: string; label: string; range: string }
> = {
  早班: { label: '早班', color: 'blue', range: '08:00 - 16:00' },
  中班: { label: '中班', color: 'green', range: '16:00 - 24:00' },
  夜班: { label: '夜班', color: 'purple', range: '24:00 - 08:00' },
  节假日班: { label: '节假日班', color: 'orange', range: '节假日值班' },
  加班: { label: '加班', color: 'red', range: '额外加班' },
};

export const weekMap = {
  0: '日',
  1: '一',
  2: '二',
  3: '三',
  4: '四',
  5: '五',
  6: '六',
};

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'date',
      label: '日期',
      component: 'DatePicker',
      componentProps: {
        picker: 'month',
      },
    },
    {
      fieldName: 'post',
      label: '岗位',
    },
    {
      fieldName: 'shift',
      label: '班次',
      component: 'Select',
      componentProps: {
        options: Object.values(shiftMap).map((item) => ({
          label: item.label,
          value: item.label,
        })),
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(dayObj: Dayjs): CustomColumnSchema<any> {
  const arr: CustomColumnSchema<any> = [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'name',
      title: '员工姓名',
      customType: 'userInfo',
      width: 120,
      custom: {
        userInfo: {
          name: (row: any) => row.name ?? '',
          img: (row: any) => row.photoPath ?? '',
          desc: (row: any) => row.positionId ?? '',
        },
      },
    },
  ];

  for (let i = 1; i <= dayObj.daysInMonth(); i++) {
    const itemDayObj = dayObj.date(i);
    arr.push({
      field: itemDayObj.format('YYYY-MM-DD'),
      title: `${itemDayObj.month() + 1}月${itemDayObj.date()}日 周${weekMap[itemDayObj.day()]}`,
      customType: 'tag',
      slots: { default: itemDayObj.format('YYYY-MM-DD') },
      width: 120,
    });
  }

  return arr;
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'employeeId',
      label: '员工',
      rules: 'required',
      component: 'ApiSelect',
      custom: {
        fetchRemoteOptions: async ({ keyword = '' }: Record<string, any>) => {
          const tableDataAll = await getOrganizationPersonListApi(
            {
              currentPage: 1,
              pageSize: 100,
            },
            {
              name: keyword,
            },
          );

          return tableDataAll.items.map((item) => ({
            label: item.name ?? '',
            value: item.id,
          }));
        },
      },
    },
    {
      fieldName: 'positionId',
      label: '岗位',
      rules: 'required',
    },
    {
      fieldName: 'shiftId',
      label: '班次',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(shiftMap).map((item) => ({
          label: item.label,
          value: item.label,
        })),
      },
    },
    {
      fieldName: 'dateRange',
      label: '日期范围',
      rules: 'required',
      component: 'RangePicker',
      custom: {
        rangePicker0: 'beginDate',
        rangePicker1: 'endDate',
      },
    },
    {
      fieldName: 'remark',
      label: '备注',
      component: 'Textarea',
    },
  ];
}
