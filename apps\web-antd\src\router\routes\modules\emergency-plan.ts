/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-29
 * @LastEditors: <PERSON><PERSON>er
 * @LastEditTime: 2025-07-29
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\router\routes\modules\emergency-plan.ts
 */
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'svg:emergency-plan',
      title: '应急预案',
    },
    name: 'emergency-plan',
    path: '/emergency-plan',
    redirect: '/emergency-plan/manage',
    children: [
      {
        meta: {
          title: '应急预案管理',
        },
        name: 'EmergencyPlanManage',
        path: '/emergency-plan/manage',
        component: () => import('#/views/emergency-plan/manage/index.vue'),
      },
    ],
  },
];

export default routes;
