import type { CmAccidentBasicInfo } from '#/api/core/emergency/evaluation';
/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-25
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\evaluation\data.ts
 */
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '事故名称',
    },
    {
      label: '事故类型',
      fieldName: 'type',
    },
    {
      label: '事故等级',
      fieldName: 'level',
    },
    {
      label: '状事故态',
      fieldName: 'status',
    },
    {
      fieldName: 'dateRange',
      label: '发生时间',
      component: 'RangePicker',
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CmAccidentBasicInfo> {
  return [
    {
      type: 'checkbox',
      width: '40',
    },
    { field: 'accidentId', title: '事故编号' },
    { field: 'accidentName', title: '事故名称' },
    { field: 'accidentType', title: '事故类型' },
    { field: 'accidentLevel', title: '事故等级' },
    {
      title: '报告类型',
      field: 'reportType',
    },
    {
      field: 'compiler',
      title: '编制人',
    },
    {
      field: 'occurTime',
      title: '发生时间',
      customType: 'dateDay',
    },
    {
      field: 'status',
      title: '状态',
    },
    {
      field: 'action',
      title: '操作',
      width: 320,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      label: '事故名称',
      fieldName: 'accidentName',
      rules: 'required',
    },
    {
      label: '事故类型',
      fieldName: 'accidentType',
      rules: 'required',
    },
    {
      label: '事故等级',
      fieldName: 'accidentLevel',
      rules: 'required',
    },
    {
      label: '发生地点',
      fieldName: 'occurLocation',
      rules: 'required',
    },
    {
      label: '发生时间',
      fieldName: 'occurTime',
      rules: 'required',
      component: 'DatePicker',
    },
    {
      label: '编制人',
      fieldName: 'compiler',
      rules: 'required',
    },
    {
      label: '编制时间',
      fieldName: 'compileTime',
      rules: 'required',
      component: 'DatePicker',
    },
    {
      label: '报告类型',
      fieldName: 'reportType',
      rules: 'required',
    },
    {
      label: '状态',
      fieldName: 'status',
      rules: 'required',
    },
    // 详细过程
    {
      label: '详细过程',
      fieldName: 'cmAccidentReportsAccidentProcess',
      component: 'Textarea',
    },
    // 事故现场的勘察情况
    {
      label: '勘察情况',
      fieldName: 'cmAccidentReportsSiteSurvey',
      component: 'Textarea',
    },
    // 事故原因分析
    {
      label: '原因分析',
      fieldName: 'cmAccidentReportsCauseAnalysis',
      component: 'Textarea',
    },
    // 责任认定
    {
      label: '责任认定',
      fieldName: 'cmAccidentReportsResponsibility',
      component: 'Textarea',
    },
    // 整改措施
    {
      label: '整改措施',
      fieldName: 'cmAccidentReportsRectification',
      component: 'Textarea',
    },
    // 附件
    {
      label: '附件',
      fieldName: 'cmAccidentAttachmentsList',
      component: 'Upload',
      custom: {
        fileDataType: 'fileList',
        uploadMultiple: true,
      },
    },
  ];
}
