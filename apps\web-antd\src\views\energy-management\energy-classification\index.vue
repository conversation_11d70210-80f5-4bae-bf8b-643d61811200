<script setup lang="ts">
import type { StatisticItem } from '#/components/statistic-card.vue';

import { onMounted, onUnmounted, ref } from 'vue';

import { FallOutlined, RiseOutlined } from '@ant-design/icons-vue';
import { Button, DatePicker } from 'ant-design-vue';
import dayjs from 'dayjs';
import * as echarts from 'echarts';

import { getEnergyClassificationApi } from '#/api/core/energy-management/energy-coal-comparison-analysis';
import StatisticCard from '#/components/statistic-card.vue';

const month = ref(dayjs().startOf('month'));

onMounted(() => {
  init();
});

async function init() {
  const res = await getEnergyClassificationApi({
    month: month.value.toISOString(),
  });
  const {
    totalFactorUsage,
    waterFactorUsage,
    electricityFactorUsage,
    waterPercentage,
    electricityPercentage,
    totalFactorUsageRatio,
    electricityFactorUsageRationull,
    waterFactorUsageRatio,
    areaFactorUsageList,
    trendChartList,
  } = res;

  total.value[0]!.value = totalFactorUsage;
  total.value[0]!.meta.changePercentage = totalFactorUsageRatio;

  total.value[1]!.value = waterFactorUsage;
  total.value[1]!.meta.changePercentage = waterFactorUsageRatio;

  total.value[2]!.value = electricityFactorUsage;
  total.value[2]!.meta.changePercentage = electricityFactorUsageRationull;

  initPieChart(electricityPercentage, waterPercentage);
  initBarChart(areaFactorUsageList);
  initLineChart(trendChartList);
}

const total = ref<StatisticItem[]>([
  {
    title: '标煤',
    value: 0,
    meta: { changePercentage: 0 },
    color: '#1890ff',
    unit: '吨',
    svgName: 'material-symbols:local-fire-department',
  },
  {
    title: '用水耗能',
    value: 0,
    meta: { changePercentage: 0 },
    color: '#13ce66',
    unit: '吨',
    svgName: 'material-symbols:water-drop',
  },
  {
    title: '用电耗能',
    value: 0,
    meta: { changePercentage: 0 },
    color: '#f7ba2a',
    unit: '度',
    svgName: 'pepicons-pop:electricity-circle-filled',
  },
]);

const pieChart = ref<any>(null);
const barChart = ref<any>(null);
const lineChart = ref<any>(null);

function initPieChart(electricityPercentage: number, waterPercentage: number) {
  if (pieChart.value) {
    pieChart.value.dispose();
    pieChart.value = null;
  }
  pieChart.value = echarts.init(
    document.querySelector('#energy-classification-pie-chart') as HTMLElement,
  );

  pieChart.value.setOption({
    title: {
      text: '能源类型占比',
      top: 10,
      left: 10,
    },
    tooltip: {
      trigger: 'item',
    },
    legend: {
      right: 10,
      top: 10,
    },
    series: [
      {
        name: '能源类型占比',
        type: 'pie',
        radius: ['50%', '70%'],
        data: [
          { value: electricityPercentage, name: '用电' },
          { value: waterPercentage, name: '用水' },
        ],
      },
    ],
  });

  onUnmounted(() => {
    if (pieChart.value) {
      pieChart.value.dispose();
      pieChart.value = null;
    }
  });
}

function initBarChart(
  list: Array<{ areaName: string; factorUsage: number }> = [],
) {
  if (barChart.value) {
    barChart.value.dispose();
    barChart.value = null;
  }

  if (!list?.length) return;

  barChart.value = echarts.init(
    document.querySelector('#energy-classification-bar-chart') as HTMLElement,
  );

  barChart.value.setOption({
    title: {
      text: '各区域能耗对比',
      top: 10,
      left: 10,
    },
    xAxis: {
      type: 'category',
      data: list.map((item) => item.areaName),
    },
    yAxis: {
      type: 'value',
    },
    series: [{ data: list.map((item) => item.factorUsage), type: 'bar' }],
  });

  onUnmounted(() => {
    if (barChart.value) {
      barChart.value.dispose();
      barChart.value = null;
    }
  });
}
function initLineChart(
  list: Array<{
    dateString: string;
    electricityUsage: number;
    factorUsage: number;
    waterUsage: number;
  }> = [],
) {
  if (lineChart.value) {
    lineChart.value.dispose();
    lineChart.value = null;
  }
  if (!list?.length) return;
  lineChart.value = echarts.init(
    document.querySelector('#energy-classification-line-chart') as HTMLElement,
  );

  lineChart.value.setOption({
    title: {
      text: '能源趋势分析',
      top: 10,
      left: 10,
    },
    xAxis: {
      type: 'category',
      data: list.map((item) => item.dateString),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '用电',
        type: 'line',
        data: list.map((item) => item.electricityUsage),
      },
      {
        name: '用水',
        type: 'line',
        data: list.map((item) => item.waterUsage),
      },
      {
        name: '标煤',
        type: 'line',
        data: list.map((item) => item.factorUsage),
      },
    ],
  });

  onUnmounted(() => {
    if (lineChart.value) {
      lineChart.value.dispose();
      lineChart.value = null;
    }
  });
}
</script>

<template>
  <div class="p-4">
    <div class="mb-4 flex items-center justify-between bg-white p-4">
      <h3 class="text-lg font-bold">能源分类分项</h3>
      <div class="flex items-center gap-2">
        <DatePicker v-model:value="month" picker="month" />
        <Button type="primary" @click="init"> 查询 </Button>
      </div>
    </div>

    <StatisticCard :total="total">
      <template #desc="{ item }">
        <p>
          <RiseOutlined
            v-if="item.meta.changePercentage >= 0"
            class="text-red-500"
          />
          <FallOutlined
            v-if="item.meta.changePercentage < 0"
            class="text-green-500"
          />
          <span
            :class="{
              'text-green-500': item.meta.changePercentage < 0,
              'text-red-500': item.meta.changePercentage >= 0,
            }"
          >
            {{ item.meta.changePercentage ?? 0 }}%
          </span>
          <span>较上月</span>
        </p>
      </template>
    </StatisticCard>

    <div class="mt-4 flex gap-4">
      <div
        class="h-[400px] flex-1 bg-white"
        id="energy-classification-pie-chart"
      ></div>
      <div
        class="h-[400px] flex-1 bg-white"
        id="energy-classification-bar-chart"
      ></div>
    </div>

    <div
      class="mt-4 h-[400px] bg-white"
      id="energy-classification-line-chart"
    ></div>
  </div>
</template>

<style scoped lang="scss"></style>
