<script lang="ts" setup>
import type { ShallowRef } from 'vue';
// eslint-disable-next-line n/no-extraneous-import
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { EmergencyTarget } from '#/api/core/emergency/target';
import type { CustomFormSchema } from '#/components/form-modal';
import type { CustomColumnSchema } from '#/components/grid-form';

import { h, ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button, message } from 'ant-design-vue';

import {
  addEmergencyTargetApi,
  deleteEmergencyTargetApi,
  editEmergencyTargetApi,
  getEmergencyTargetDetailApi,
  getEmergencyTargetListApi,
} from '#/api/core/emergency/target';
import GridForm from '#/components/grid-form/index.vue';
import RemoveButton from '#/components/remove-button.vue';

import { getColumnsScheme, getFormOption, getTopSearchScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const topSearchScheme: ShallowRef<CustomFormSchema[]> =
  shallowRef(getTopSearchScheme());
const columnsScheme: ShallowRef<CustomColumnSchema<EmergencyTarget>> =
  shallowRef(getColumnsScheme());
const formOption: ShallowRef<CustomFormSchema[]> = shallowRef(getFormOption());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<EmergencyTarget>,
  fromData: any,
) {
  return getEmergencyTargetListApi(param.page, fromData);
}

/**
 * @description: 删除
 */
function deletePersonnel(row: EmergencyTarget) {
  return deleteEmergencyTargetApi(row.targetId).then(() => {
    message.success('删除成功');
    gridFormRef.value?.gridApi.reload();
  });
}

/**
 * @description: 编辑
 */
function editPersonnel(row: EmergencyTarget) {
  gridFormRef.value?.gridApi.setLoading(true);
  getEmergencyTargetDetailApi(row.targetId)
    .then((res) => {
      gridFormRef.value?.formModalRef?.open({
        title: '防护目标基本信息',
        formData: {
          ...res,
          // 应急资源
          cmProtectionTargetResourcesFireResource:
            res.cmProtectionTargetResources?.fireResource,
          cmProtectionTargetResourcesMedicalResource:
            res.cmProtectionTargetResources?.medicalResource,
          cmProtectionTargetResourcesEvacuationResource:
            res.cmProtectionTargetResources?.evacuationResource,
          cmProtectionTargetResourcesOtherResources:
            res.cmProtectionTargetResources?.otherResources,

          // 照片
          cmProtectionTargetPhoto: res.cmProtectionTargetPhoto?.photoPath,
        },
        meta: { type: 'edit', raw: row },
      });
    })
    .finally(() => {
      gridFormRef.value?.gridApi.setLoading(false);
    });
}

/**
 * @description: 编辑或新建
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: EmergencyTarget;
}): Promise<boolean> {
  // 应急资源
  const cmProtectionTargetResources = {
    targetId: param.meta.raw?.targetId,
    fireResource: param.formData.cmProtectionTargetResourcesFireResource,
    medicalResource: param.formData.cmProtectionTargetResourcesMedicalResource,
    evacuationResource:
      param.formData.cmProtectionTargetResourcesEvacuationResource,
    otherResources: param.formData.cmProtectionTargetResourcesOtherResources,
  };
  param.formData.cmProtectionTargetResources = cmProtectionTargetResources;
  Reflect.deleteProperty(
    param.formData,
    'cmProtectionTargetResourcesFireResource',
  );
  Reflect.deleteProperty(
    param.formData,
    'cmProtectionTargetResourcesMedicalResource',
  );
  Reflect.deleteProperty(
    param.formData,
    'cmProtectionTargetResourcesEvacuationResource',
  );
  Reflect.deleteProperty(
    param.formData,
    'cmProtectionTargetResourcesOtherResources',
  );

  // 照片
  const cmProtectionTargetPhoto = {
    targetId: param.meta.raw?.targetId,
    photoPath: param.formData.cmProtectionTargetPhoto ?? '',
  };
  param.formData.cmProtectionTargetPhoto = cmProtectionTargetPhoto;

  if (param.meta.type === 'create')
    addEmergencyTargetApi({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        gridFormRef.value?.gridApi.reload();
        return true;
      })
      .catch(() => false);
  else if (param.meta.type === 'edit')
    editEmergencyTargetApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        gridFormRef.value?.gridApi.query();
        return true;
      })
      .catch(() => false);

  return Promise.resolve(true);
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="防护目标信息列表"
        :columns-scheme="columnsScheme"
        :form-option="formOption"
        :query="query"
        :top-search-scheme="topSearchScheme"
        :submit="onSubmit"
      >
        <template #toolbar-tools>
          <Button
            type="primary"
            shape="circle"
            :icon="h(PlusOutlined)"
            @click="
              () => {
                gridFormRef?.formModalRef?.open({
                  title: '防护目标基本信息',
                  meta: { type: 'create' },
                });
              }
            "
          />
        </template>
        <template #action="{ row }">
          <Button type="link" @click="editPersonnel(row)"> 编辑 </Button>
          <RemoveButton @confirm="deletePersonnel(row)" />
        </template>
      </GridForm>
    </template>
  </Page>
</template>
