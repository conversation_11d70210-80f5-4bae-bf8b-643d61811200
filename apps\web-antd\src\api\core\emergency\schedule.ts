import type { CmPersonnel } from './organization';

import dayjs from 'dayjs';

/*
 * @Author: Strayer
 * @Date: 2025-07-24
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-25
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\api\core\emergency\schedule.ts
 */
import { requestClient } from '#/api/request';

/**
 * CmSchedule对象
 *
 * CmSchedule
 */
export type CmSchedule = {
  /**
   * 排班开始日期,新增需要
   */
  beginDate?: string;
  /**
   * 记录创建时间（自动生成）
   */
  createdAt?: string;
  /**
   * 排班对应的员工ID
   */
  employeeId: number;
  /**
   * 排班开始日期,新增需要
   */
  endDate?: string;
  /**
   * 员工信息
   */
  personnel?: CmPersonnel;
  /**
   * 排班对应的岗位ID
   */
  positionId?: string;
  /**
   * 排班的备注信息
   */
  remark?: string;
  /**
   * 排班的日期,新增不用传
   */
  scheduleDate: string;
  /**
   * 排班的唯一标识（主键）
   */
  scheduleId: number;
  /**
   * 排班对应的班次ID
   */
  shiftId?: string;
  /**
   * 记录更新时间（自动更新）
   */
  updatedAt?: string;
};

/** 查询应急排班列表 */
export async function getEmergencyScheduleListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClient
    .get<CmSchedule[]>('/cm-schedule', {
      params: {
        pageNumber: param.currentPage,
        pageSize: param.pageSize,
        ...search,
      },
    })
    .then((res) => {
      // 组装为前端格式：
      // 每个员工为一条记录
      // 记录里面固定包含姓名、头像、岗位字段
      // 每条记录再动态拼接上日的排班数据
      const personnelMap: Record<
        string,
        {
          [key: string]: any; // 日：{value: '早班', scheduleId: 1} []
          employeeId: number;
          name: string;
          photoPath: string;
          positionId?: string;
        }
      > = {};

      for (const item of res) {
        if (!personnelMap[item.employeeId])
          personnelMap[item.employeeId] = {
            employeeId: item.employeeId,
            name: item.personnel?.name ?? '',
            photoPath: item.personnel?.photoPath ?? '',
            positionId: item.positionId,
          };

        const scheduleDate = dayjs(new Date(item.scheduleDate)).format(
          'YYYY-MM-DD',
        );
        if (!personnelMap[item.employeeId]![scheduleDate])
          personnelMap[item.employeeId]![scheduleDate] = [];

        personnelMap[item.employeeId]![scheduleDate].push({
          value: item.shiftId,
          scheduleId: item.scheduleId,
        });
      }

      const personnelArr = Object.values(personnelMap);

      return {
        items: personnelArr,
        total: personnelArr.length,
      };
    });
}

/** 获取应急排班详情 */
export async function getEmergencyScheduleDetailApi(scheduleId: number) {
  return requestClient.get<CmSchedule>(`/cm-schedule/detail/${scheduleId}`);
}

/**
 * 新增应急排班
 */
export async function addEmergencyScheduleApi(data: CmSchedule) {
  return requestClient.post<CmSchedule>('/cm-schedule', data);
}

/**
 * 修改应急排班
 */
export async function editEmergencyScheduleApi(data: CmSchedule) {
  return requestClient.put<CmSchedule>('/cm-schedule', data);
}

/**
 * 删除应急排班
 */
export async function deleteEmergencyScheduleApi(scheduleId: number) {
  return requestClient.delete<CmSchedule>(`/cm-schedule/${scheduleId}`);
}
