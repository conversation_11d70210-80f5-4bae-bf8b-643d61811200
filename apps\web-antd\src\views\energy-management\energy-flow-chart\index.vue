<script setup lang="ts">
import type { PickerMode } from 'ant-design-vue/es/vc-picker/interface';

import { onMounted, onUnmounted, ref } from 'vue';

import { Button, DatePicker, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import * as echarts from 'echarts';

import { getEnergyFlowChartData } from '#/api/core/energy-management/energy-coal-comparison-analysis';

interface AreaData {
  areaId: string;
  areaName: string;
  deviceList: Device[];
}

interface Device {
  deviceId: string;
  deviceName: string;
  factorUsage: number;
  usage: number;
}

interface EnergyFlowChartData {
  electricityAreaList: AreaData[];
  name: string;
  waterAreaList: AreaData[];
}

const date = ref(dayjs().startOf('month'));

onMounted(() => {
  init();
});

async function init() {
  const res: EnergyFlowChartData = await getEnergyFlowChartData({
    date: date.value.toISOString(),
    type: dateType.value,
  });

  initChart1(res);
  initChart2(res);
}

const dateTypeOptions = [
  {
    label: '按年',
    value: 1,
  },
  {
    label: '按月',
    value: 2,
  },
  {
    label: '按日',
    value: 3,
  },
];

const dateType = ref(2);
const datePickerType = ref<PickerMode>('month');

function dateTypeChange(value: any) {
  dateType.value = value;
  switch (value) {
    case 1: {
      datePickerType.value = 'year';
      date.value = dayjs(date.value).startOf('year');

      break;
    }
    case 2: {
      datePickerType.value = 'month';
      date.value = dayjs(date.value).startOf('month');

      break;
    }
    case 3: {
      datePickerType.value = 'date';
      date.value = dayjs(date.value).startOf('day');

      break;
    }
  }
}

const chart1 = ref<any>(null);
function initChart1(data: EnergyFlowChartData) {
  if (chart1.value) {
    chart1.value.dispose();
    chart1.value = null;
  }

  const nameList = [
    {
      name: data.name,
      usage: (data.electricityAreaList ?? [])
        .flatMap((e) => e.deviceList ?? [])
        .map((e) => e.usage)
        .reduce((total, n) => total + n, 0),
      factorUsage: (data.electricityAreaList ?? [])
        .flatMap((e) => e.deviceList ?? [])
        .map((e) => e.factorUsage)
        .reduce((total, n) => total + n, 0),
    },
  ];
  const links = [];

  for (const each of data.electricityAreaList) {
    nameList.push({
      name: each.areaName,
      usage: (each.deviceList ?? [])
        .map((e) => e.usage)
        .reduce((total, n) => total + n, 0),
      factorUsage: (each.deviceList ?? [])
        .map((e) => e.factorUsage)
        .reduce((total, n) => total + n, 0),
    });

    links.push({
      source: data.name,
      target: each.areaName,
      value: (each.deviceList ?? [])
        .map((e) => e.usage)
        .reduce((total, n) => total + n, 0),
      factorUsage: (each.deviceList ?? [])
        .map((e) => e.factorUsage)
        .reduce((total, n) => total + n, 0),
    });

    for (const device of each.deviceList ?? []) {
      if (device.deviceName) {
        if (!nameList.some((e) => e.name === device.deviceName)) {
          nameList.push({
            name: device.deviceName,
            usage: data.electricityAreaList
              .flatMap((e) => e.deviceList ?? [])
              .filter((e) => e.deviceName === device.deviceName)
              .map((e) => e.usage)
              .reduce((total, n) => total + n, 0),
            factorUsage: data.electricityAreaList
              .flatMap((e) => e.deviceList ?? [])
              .filter((e) => e.deviceName === device.deviceName)
              .map((e) => e.factorUsage)
              .reduce((total, n) => total + n, 0),
          });
        }
      } else {
        nameList.push({
          name: device.deviceId,
          usage: data.electricityAreaList
            .flatMap((e) => e.deviceList ?? [])
            .filter((e) => e.deviceId === device.deviceId)
            .map((e) => e.usage)
            .reduce((total, n) => total + n, 0),
          factorUsage: data.electricityAreaList
            .flatMap((e) => e.deviceList ?? [])
            .filter((e) => e.deviceId === device.deviceId)
            .map((e) => e.factorUsage)
            .reduce((total, n) => total + n, 0),
        });
      }

      links.push({
        source: each.areaName,
        target: device.deviceName || device.deviceId,
        value: device.usage,
        factorUsage: device.factorUsage,
      });
    }
  }

  if (!data) return;

  chart1.value = echarts.init(
    document.querySelector('#energy-flow-chart1') as HTMLElement,
  );

  chart1.value.setOption({
    title: {
      text: '用电能流图',
    },
    tooltip: {
      formatter(params: any) {
        if (params.dataType === 'edge') {
          return `<div class="flex gap-4">
         <div> ${params.data.source} -- ${params.data.target}  </div>
         <div class="font-semibold">
          <div>用电 ${params.data.value} 度</div>
          <div>标煤 ${params.data.factorUsage} 吨</div>
         </div>
        </div>`;
        } else if (params.dataType === 'node') {
          return `<div class="flex gap-4">
           <div> ${params.data.name}  </div>
           <div class="font-semibold">
            <div>用电 ${params.data.usage} 度</div>
            <div>标煤 ${params.data.factorUsage} 吨</div>
           </div>
          </div>`;
        }
      },
    },
    series: {
      type: 'sankey',
      layout: 'none',
      emphasis: {
        focus: 'adjacency',
      },
      data: nameList,
      links,
      levels: [
        {
          depth: 0,
          itemStyle: {
            color: '#fbb4ae',
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
          },
        },
        {
          depth: 1,
          itemStyle: {
            color: '#b3cde3',
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
          },
        },
        {
          depth: 2,
          itemStyle: {
            color: '#ccebc5',
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
          },
        },
        {
          depth: 3,
          itemStyle: {
            color: '#decbe4',
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
          },
        },
      ],
      lineStyle: {
        curveness: 0.5,
      },
    },
  });

  onUnmounted(() => {
    if (chart1.value) {
      chart1.value.dispose();
      chart1.value = null;
    }
  });
}

const chart2 = ref<any>(null);
function initChart2(data: EnergyFlowChartData) {
  if (chart2.value) {
    chart2.value.dispose();
    chart2.value = null;
  }

  if (!data) return;

  chart2.value = echarts.init(
    document.querySelector('#energy-flow-chart2') as HTMLElement,
  );

  const nameList = [
    {
      name: data.name,
      usage: (data.waterAreaList ?? [])
        .flatMap((e) => e.deviceList ?? [])
        .map((e) => e.usage)
        .reduce((total, n) => total + n, 0),
      factorUsage: (data.waterAreaList ?? [])
        .flatMap((e) => e.deviceList ?? [])
        .map((e) => e.factorUsage)
        .reduce((total, n) => total + n, 0),
    },
  ];
  const links = [];

  for (const each of data.waterAreaList) {
    nameList.push({
      name: each.areaName,
      usage: (each.deviceList ?? [])
        .map((e) => e.usage)
        .reduce((total, n) => total + n, 0),
      factorUsage: (each.deviceList ?? [])
        .map((e) => e.factorUsage)
        .reduce((total, n) => total + n, 0),
    });

    links.push({
      source: data.name,
      target: each.areaName,
      value: (each.deviceList ?? [])
        .map((e) => e.usage)
        .reduce((total, n) => total + n, 0),
      factorUsage: (each.deviceList ?? [])
        .map((e) => e.factorUsage)
        .reduce((total, n) => total + n, 0),
    });

    for (const device of each.deviceList ?? []) {
      if (device.deviceName) {
        if (!nameList.some((e) => e.name === device.deviceName)) {
          nameList.push({
            name: device.deviceName,
            usage: data.waterAreaList
              .flatMap((e) => e.deviceList ?? [])
              .filter((e) => e.deviceName === device.deviceName)
              .map((e) => e.usage)
              .reduce((total, n) => total + n, 0),
            factorUsage: data.waterAreaList
              .flatMap((e) => e.deviceList ?? [])
              .filter((e) => e.deviceName === device.deviceName)
              .map((e) => e.factorUsage)
              .reduce((total, n) => total + n, 0),
          });
        }
      } else {
        nameList.push({
          name: device.deviceId,
          usage: data.waterAreaList
            .flatMap((e) => e.deviceList ?? [])
            .filter((e) => e.deviceId === device.deviceId)
            .map((e) => e.usage)
            .reduce((total, n) => total + n, 0),
          factorUsage: data.waterAreaList
            .flatMap((e) => e.deviceList ?? [])
            .filter((e) => e.deviceId === device.deviceId)
            .map((e) => e.factorUsage)
            .reduce((total, n) => total + n, 0),
        });
      }

      links.push({
        source: each.areaName,
        target: device.deviceName || device.deviceId,
        value: device.usage,
        factorUsage: device.factorUsage,
      });
    }
  }

  chart2.value.setOption({
    title: {
      text: '用水能流图',
    },
    tooltip: {
      formatter(params: any) {
        if (params.dataType === 'edge') {
          return `<div class="flex gap-4">
         <div> ${params.data.source} -- ${params.data.target}  </div>
         <div class="font-semibold">
          <div>用水 ${params.data.value} 吨</div>
          <div>标煤 ${params.data.factorUsage} 吨</div>
         </div>
        </div>`;
        } else if (params.dataType === 'node') {
          return `<div class="flex gap-4">
           <div> ${params.data.name}  </div>
           <div class="font-semibold">
            <div>用水 ${params.data.usage} 吨</div>
            <div>标煤 ${params.data.factorUsage} 吨</div>
           </div>
          </div>`;
        }
      },
    },
    series: {
      type: 'sankey',
      layout: 'none',
      emphasis: {
        focus: 'adjacency',
      },
      data: nameList,
      links,

      levels: [
        {
          depth: 0,
          itemStyle: {
            color: '#fbb4ae',
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
          },
        },
        {
          depth: 1,
          itemStyle: {
            color: '#b3cde3',
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
          },
        },
        {
          depth: 2,
          itemStyle: {
            color: '#ccebc5',
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
          },
        },
        {
          depth: 3,
          itemStyle: {
            color: '#decbe4',
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
          },
        },
      ],
      lineStyle: {
        curveness: 0.5,
      },
    },
  });

  onUnmounted(() => {
    if (chart2.value) {
      chart2.value.dispose();
      chart2.value = null;
    }
  });
}
</script>

<template>
  <div class="flex h-full flex-col gap-4 p-4">
    <div class="flex items-center justify-between bg-white p-4">
      <h3 class="text-lg font-bold">能流图</h3>
      <div class="flex items-center gap-2">
        <DatePicker
          v-model:value="date"
          :picker="datePickerType"
          :allow-clear="false"
        />
        <Select
          v-model:value="dateType"
          :options="dateTypeOptions"
          @change="dateTypeChange"
        />

        <Button type="primary" @click="init"> 查询 </Button>
      </div>
    </div>

    <div class="flex-1 bg-white" id="energy-flow-chart1"></div>
    <div class="flex-1 bg-white" id="energy-flow-chart2"></div>
  </div>
</template>

<style scoped lang="scss"></style>
