import type { Graph } from '@antv/x6';

import { Stencil } from '@antv/x6-plugin-stencil';

export function initStencil(graph: Graph) {
  // #region 初始化 stencil
  const stencil = new Stencil({
    title: '流程图',
    target: graph,
    stencilGraphWidth: 200,
    stencilGraphHeight: 180,
    collapsable: true,
    groups: [
      {
        title: '基础流程图',
        name: 'group1',
      },
      {
        title: '系统设计图',
        name: 'group2',
        graphHeight: 250,
        layoutOptions: {
          rowHeight: 70,
        },
      },
    ],
    layoutOptions: {
      columns: 2,
      columnWidth: 80,
      rowHeight: 55,
    },
  });
  document.querySelector('#stencil')!.append(stencil.container);
  // #endregion

  const r1 = graph.createNode({
    shape: 'custom-rect',
    label: '开始',
    attrs: {
      body: {
        rx: 20,
        ry: 26,
      },
    },
  });
  const r2 = graph.createNode({
    shape: 'custom-rect',
    label: '过程',
  });
  const r3 = graph.createNode({
    shape: 'custom-rect',
    attrs: {
      body: {
        rx: 6,
        ry: 6,
      },
    },
    label: '可选过程',
  });
  const r4 = graph.createNode({
    shape: 'custom-polygon',
    attrs: {
      body: {
        refPoints: '0,10 10,0 20,10 10,20',
      },
    },
    label: '决策',
  });
  const r5 = graph.createNode({
    shape: 'custom-polygon',
    attrs: {
      body: {
        refPoints: '10,0 40,0 30,20 0,20',
      },
    },
    label: '数据',
  });
  const r6 = graph.createNode({
    shape: 'custom-circle',
    label: '连接',
  });
  stencil.load([r1, r2, r3, r4, r5, r6], 'group1');

  // 图片+文本
  // const imageShapes = [
  //   {
  //     label: 'Client',
  //     image:
  //       'https://gw.alipayobjects.com/zos/bmw-prod/687b6cb9-4b97-42a6-96d0-34b3099133ac.svg',
  //   },
  //   {
  //     label: 'Http',
  //     image:
  //       'https://gw.alipayobjects.com/zos/bmw-prod/dc1ced06-417d-466f-927b-b4a4d3265791.svg',
  //   },
  // ];
  // const imageNodes = imageShapes.map((item) =>
  //   graph.createNode({
  //     shape: 'custom-image',
  //     label: item.label,
  //     attrs: {
  //       image: {
  //         'xlink:href': item.image,
  //       },
  //     },
  //   }),
  // );
  // stencil.load(imageNodes, 'group2');
}
