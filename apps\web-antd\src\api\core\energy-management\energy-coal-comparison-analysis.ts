import { requestClient } from '#/api/request';

// 对比分析-趋势柱状图
export async function getTrendBarChartDataApi(): Promise<any[]> {
  return requestClient.get(
    '/energyConsumptionMonitoring/comparisonAnalysis/trendBarChart',
  );
}

// 对比分析-趋势折线图
export async function getTrendLineChartDataApi(
  params: Record<string, any>,
): Promise<any[]> {
  return requestClient.get(
    '/energyConsumptionMonitoring/comparisonAnalysis/trendLineChart',
    {
      params,
    },
  );
}

// 对比分析-数据详情列表
export async function getComparisonAnalysisListApi(
  params: Record<string, any>,
): Promise<any[]> {
  return requestClient.get(
    '/energyConsumptionMonitoring/comparisonAnalysis/list',
    {
      params,
    },
  );
}

// 能源分类
export async function getEnergyClassificationApi(
  params: Record<string, any>,
): Promise<any> {
  return requestClient.get('/energyConsumptionMonitoring/energyDataAnalysis', {
    params,
  });
}

// 能流图
export async function getEnergyFlowChartData(
  params: Record<string, any>,
): Promise<any> {
  return requestClient.get('/energyConsumptionMonitoring/energyChart', {
    params,
  });
}
