/*
 * @Author: Strayer
 * @Date: 2025-08-27
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-27
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\api\core\emergency\response.ts
 */

import { requestClient, requestClientGridPage } from '#/api/request';

/**
 * 应急事件对象
 */
export type CmIncident = {
  /**
   * 唯一标识一条应急事件
   */
  cmIncidentId: number;
  /**
   * 事件类型（如化工厂泄漏、建筑坍塌等）
   */
  cmIncidentType?: string;
  /**
   * 记录创建时间
   */
  createTime?: string;
  /**
   * 事件详细描述
   */
  description?: string;
  /**
   * 事件发生地点
   */
  location?: string;
  /**
   * 事件发生时间
   */
  occurTime?: string;
  /**
   * 处理优先级（1-低，2-中，3-高）
   */
  priority?: number;
  /**
   * 响应级别（1-一级，2-二级，3-三级）
   */
  responseLevel?: number;
  /**
   * 关联员工表的负责人ID
   */
  responsibleId?: number;
  /**
   * 严重程度（1-一般，2-中等，3-重大）
   */
  severity?: number;
  /**
   * 事件状态（1-处理中，2-已解决，3-已关闭）
   */
  status?: number;
  /**
   * 记录更新时间
   */
  updateTime?: string;
};

/** 获取应急事件列表 */
export async function getEmergencyEventListApi(
  param: {
    currentPage: number;
    pageSize: number;
  },
  search: Record<string, any>,
) {
  return requestClientGridPage.get<{
    items: CmIncident[];
    total: number;
  }>('/cm-incident', {
    params: {
      pageNumber: param.currentPage,
      pageSize: param.pageSize,
      ...search,
    },
  });
}

/** 获取应急事件详情 */
export async function getEmergencyEventDetailApi(incidentId: number) {
  return requestClient.get<CmIncident>(`/cm-incident/detail/${incidentId}`);
}

/** 新增应急事件 */
export async function addEmergencyEventApi(data: CmIncident) {
  return requestClient.post<CmIncident>('/cm-incident', data);
}

/** 修改应急事件 */
export async function editEmergencyEventApi(data: CmIncident) {
  return requestClient.put<CmIncident>('/cm-incident', data);
}

/** 删除应急事件 */
export async function deleteEmergencyEventApi(incidentId: number) {
  return requestClient.delete<CmIncident>(`/cm-incident/${incidentId}`);
}

/** 统计 */
export async function getEmergencyEventCountApi() {
  return requestClient.post<{
    accidentTypeMap?: Record<
      string,
      {
        changeFromYesterday: number;
        count: number;
      }
    >;
  }>('/cm-incident/statistics');
}
