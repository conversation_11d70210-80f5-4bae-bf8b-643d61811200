import type { EnMediumType } from '#/api/core/energy-management/energy-medium-type';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    // {
    //   fieldName: 'mediumName',
    //   label: '介质名称',
    // },
    // {
    //   fieldName: 'mediumCode',
    //   label: '介质代码',
    // },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<EnMediumType> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'mediumId', title: 'ID', visible: false },
    // 介质名称
    { field: 'mediumName', title: '介质名称' },
    // 介质代码
    { field: 'mediumCode', title: '介质代码' },
    // 计量单位
    { field: 'unit', title: '计量单位' },
    // 图标类
    // 状态
    {
      field: 'status',
      title: '状态',
      formatter: ({ cellValue }) => {
        if (cellValue === 1) {
          return '启用';
        }
        if (cellValue === 0) {
          return '禁用';
        }
        return '';
      },
    },
    { field: 'iconClasses', title: '图标类' },
    // 创建时间
    {
      field: 'createTime',
      title: '创建时间',
      customType: 'date',
      visible: false,
    },
    // 更新时间
    {
      field: 'updateTime',
      title: '更新时间',
      customType: 'date',
      visible: false,
    },
    {
      field: 'action',
      title: '操作',
      width: 180,
    },
  ];
}

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'mediumName',
      label: '介质名称',
      rules: 'required',
    },
    {
      fieldName: 'mediumCode',
      label: '介质代码',
      rules: 'required',
    },
    {
      fieldName: 'unit',
      label: '计量单位',
      rules: 'required',
    },
    {
      fieldName: 'iconClasses',
      label: '图标类',
      component: 'Input',
      componentProps: {
        placeholder: '请输入图标类',
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ];
}
