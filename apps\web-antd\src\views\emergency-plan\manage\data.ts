import type { Component } from 'vue';

/*
 * @Author: <PERSON>rayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-07-30
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency-plan\manage\data.ts
 */
import type { CmEmergencyPlans } from '#/api/core/emergency-plan/manage';
import type {
  CustomColumnSchema,
  CustomFormSchema,
} from '#/components/grid-form';

import {
  SvgCheckCircleIcon,
  SvgFileIcon,
  SvgPublishIcon,
  SvgRejectIcon,
} from '@vben/icons';
// 状态
export const StatusMap: Record<
  string,
  {
    color: string;
    component?: Component;
    label: string;
    next?: string; // 下一级的key
    nextLabel?: string; // 按钮label
    value: string;
  }
> = {
  待审批: {
    value: '待审批',
    label: '待审批',
    color: 'purple',
    next: '已通过',
    nextLabel: '审批',
    component: SvgFileIcon,
  },
  已通过: {
    value: '已通过',
    label: '已通过',
    color: 'green',
    next: '已发布',
    nextLabel: '发布',
    component: SvgCheckCircleIcon,
  },
  已发布: {
    value: '已发布',
    label: '已发布',
    color: 'blue',
    component: SvgPublishIcon,
  },
  已驳回: {
    value: '已驳回',
    label: '已驳回',
    color: 'red',
    next: '待审批',
    nextLabel: '重新发起',
    component: SvgRejectIcon,
  },
};

// 顶部搜索表单
export function getTopSearchScheme(): CustomFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '预案名称',
    },
    {
      fieldName: 'type',
      label: '事故类型',
    },
    {
      fieldName: 'level',
      label: '预案等级',
    },
    {
      fieldName: 'status',
      label: '预案状态',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },
  ];
}

// 表格列头
export function getColumnsScheme(): CustomColumnSchema<CmEmergencyPlans> {
  return [
    { title: '序号', type: 'seq', width: 50 },
    // 预案编号
    {
      field: 'planCode',
      title: '预案编号',
    },
    // 预案名称
    {
      field: 'planName',
      title: '预案名称',
    },
    // 事故类型
    {
      field: 'accidentType',
      title: '事故类型',
    },
    // 预案等级
    {
      field: 'planLevel',
      title: '预案等级',
    },
    // 编制部门
    {
      field: 'department',
      title: '编制部门',
    },
    // 当前状态
    {
      field: 'status',
      title: '当前状态',
      customType: 'tag',
      formatter: (param: any) => {
        const cellValue = param.row.status;
        return [StatusMap[cellValue] ?? cellValue ?? ''] as any;
      },
    },

    {
      field: 'action',
      title: '操作',
      width: 320,
    },
  ];
}

export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'planName',
      label: '预案名称',
      rules: 'required',
    },
    {
      fieldName: 'planCode',
      label: '预案编号',
      rules: 'required',
    },
    {
      fieldName: 'accidentType',
      label: '事故类型',
      rules: 'required',
    },
    {
      fieldName: 'planLevel',
      label: '预案等级',
      rules: 'required',
    },
    {
      fieldName: 'department',
      label: '编制部门',
      rules: 'required',
    },
    {
      fieldName: 'responsibility',
      label: '预案负责人',
      rules: 'required',
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      rules: 'required',
    },
    {
      fieldName: 'status',
      label: '当前状态',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
        placeholder: '自动生成',
      },
      disabled: true,
    },
    {
      fieldName: 'accidentDesc',
      label: '事故描述',
      component: 'Textarea',
      componentProps: {
        rows: 3,
        placeholder: '清详细描述可能发生的事故情况',
      },
    },
    {
      fieldName: 'scope',
      label: '适用范围',
      component: 'Textarea',
      componentProps: {
        rows: 3,
        placeholder: '请描述本预案的使用范围',
      },
    },
  ];
}
