<script setup lang="ts">
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { CmIncident } from '#/api/core/emergency/response';

import { ref, shallowRef, useTemplateRef } from 'vue';

import { Button, message, Popconfirm } from 'ant-design-vue';
import { clone } from 'remeda';

import {
  addEmergencyEventApi,
  deleteEmergencyEventApi,
  editEmergencyEventApi,
} from '#/api/core/emergency/response';
import { formatFormOption } from '#/components/form-modal';
import FormModal from '#/components/form-modal/index.vue';

import { getFormOption } from './data';

const emit = defineEmits<{
  (e: 'hadEdit'): void;
}>();

const manageDetailObj = shallowRef<CmIncident>();
const pageType = ref<'add' | 'edit'>('add');

// 新建或编辑表单弹窗
const formModalRef =
  useTemplateRef<ComponentExposed<typeof FormModal>>('formModalRef');

const formOption = formatFormOption(getFormOption(), {
  keywordMap: ref({}),
  fetchingMap: ref({}),
  latestFormData: shallowRef({}),
});

/**
 * @description: 编辑
 * @param {*} param
 */
function onSubmit(param: {
  formData: any;
  meta: any;
  raw: any;
}): Promise<boolean> {
  if (!param.formData.responsibleId) {
    param.formData.responsibleId = 1;
  }

  if (param.meta.type === 'edit') {
    editEmergencyEventApi({
      ...param.raw,
      ...param.formData,
    })
      .then(() => {
        message.success('更新成功');
        emit('hadEdit');
        return true;
      })
      .catch(() => false);
  } else if (param.meta.type === 'add') {
    addEmergencyEventApi({
      ...param.formData,
    })
      .then(() => {
        message.success('新增成功');
        emit('hadEdit');
        return true;
      })
      .catch(() => false);
  }

  return Promise.resolve(true);
}

/** 删除 */
function deleteSchedule() {
  deleteEmergencyEventApi(manageDetailObj.value?.cmIncidentId!)
    .then(() => {
      message.success('删除成功');
      formModalRef.value?.modalApi.close();
      emit('hadEdit');
    })
    .catch(() => false);
}

function open(param?: CmIncident) {
  manageDetailObj.value = param;
  pageType.value = param ? 'edit' : 'add';

  formModalRef.value?.open({
    title: param ? '事件详情' : '新增事件',
    formData: param,
    meta: { type: param ? 'edit' : 'add', raw: clone(param) },
  });
}

defineExpose({
  open,
});
</script>

<template>
  <!-- 详情弹窗 -->
  <FormModal ref="formModalRef" :form-option="formOption" :submit="onSubmit">
    <template v-if="pageType === 'edit'" #center-footer>
      <Popconfirm title="是否确认删除?" @confirm="deleteSchedule()">
        <Button type="primary" danger>删除</Button>
      </Popconfirm>
    </template>
  </FormModal>
</template>
