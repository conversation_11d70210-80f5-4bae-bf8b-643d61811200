import { requestClient } from '#/api/request';

/**
 * 标煤折算系数
 * EnCoalConversionFactor
 */
export type EnCoalConversionFactor = {
  conversionFactor: number;
  createTime: string;
  effectiveDate: string;
  factorId: string;
  mediumId: string;
  source: string;
  status: number;
  updateTime: string;
};

/**
 * 标煤折算系数 DTO（包含关联数据）
 * CoalConversionFactorDTO
 */
export type CoalConversionFactorDTO = {
  conversionFactor: number;
  createTime: string;
  effectiveDate: string;
  factorId: string;
  mediumId: string;
  mediumName: string;
  source: string;
  status: number;
  updateTime: string;
};

/**
 * 获取标煤折算系数列表
 */
export async function getCoalConversionFactorListApi() {
  return requestClient.get<CoalConversionFactorDTO[]>(
    '/energyManagementBaseData/coalConversionFactor/list',
  );
}

/**
 * 新增标煤折算系数
 */
export async function addCoalConversionFactorApi(data: EnCoalConversionFactor) {
  return requestClient.post<EnCoalConversionFactor>(
    '/energyManagementBaseData/coalConversionFactor',
    data,
  );
}

/**
 * 修改标煤折算系数
 */
export async function editCoalConversionFactorApi(
  data: EnCoalConversionFactor,
) {
  return requestClient.put<EnCoalConversionFactor>(
    '/energyManagementBaseData/coalConversionFactor',
    data,
  );
}

/**
 * 删除标煤折算系数
 */
export async function deleteCoalConversionFactorApi(factorId: string) {
  return requestClient.delete<string>(
    `/energyManagementBaseData/coalConversionFactor/${factorId}`,
  );
}
