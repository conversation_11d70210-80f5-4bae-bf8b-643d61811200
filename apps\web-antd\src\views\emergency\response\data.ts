/*
 * @Author: Strayer
 * @Date: 2025-07-09
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-27
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\emergency\response\data.ts
 */
import type { CustomFormSchema } from '#/components/grid-form';

// 严重程度map
export const SeverityMap: Record<
  string,
  { color: string; label: string; value: number }
> = {
  1: {
    value: 1,
    label: '一般',
    color: 'blue',
  },
  2: {
    value: 2,
    label: '中等',
    color: 'orange',
  },
  3: {
    value: 3,
    label: '重大',
    color: 'red',
  },
};

// 状态map
export const StatusMap: Record<
  string,
  { color: string; label: string; value: number }
> = {
  1: {
    value: 1,
    label: '处理中',
    color: 'orange',
  },
  2: {
    value: 2,
    label: '已解决',
    color: 'green',
  },
  3: {
    value: 3,
    label: '已关闭',
    color: 'gray',
  },
};

// 优先级map
export const PriorityMap: Record<
  string,
  { color: string; label: string; value: number }
> = {
  1: {
    value: 1,
    label: '低',
    color: 'blue',
  },
  2: {
    value: 2,
    label: '中',
    color: 'orange',
  },
  3: {
    value: 3,
    label: '高',
    color: 'red',
  },
};

// 响应级别map
export const ResponseLevelMap: Record<
  string,
  { color: string; label: string; value: number }
> = {
  1: {
    value: 1,
    label: '一级',
    color: 'blue',
  },
  2: {
    value: 2,
    label: '二级',
    color: 'orange',
  },
  3: {
    value: 3,
    label: '三级',
    color: 'red',
  },
};

// 顶部搜索表单
// export function getTopSearchScheme(): CustomFormSchema[] {
//   return [
//     {
//       fieldName: 'type',
//       label: '事件类型',
//     },
//   ];
// }

// 表格列头
// export function getColumnsScheme(): CustomColumnSchema<CmIncident> {
//   return [
//     { title: '序号', type: 'seq', width: 50 },
//     {
//       field: 'cmIncidentId',
//       title: '事件编号',
//     },
//     {
//       field: 'cmIncidentType',
//       title: '事件类型',
//     },
//     {
//       field: 'location',
//       title: '发生地点',
//     },
//     {
//       field: 'occurTime',
//       title: '发生时间',
//       customType: 'date',
//     },
//     {
//       field: 'severity',
//       title: '严重程度',
//       customType: 'tag',
//       formatter: (param: any) => {
//         const cellValue = param.row.severity;
//         return [SeverityMap[cellValue] ?? cellValue ?? ''] as any;
//       },
//     },
//     {
//       field: 'status',
//       title: '状态',
//       customType: 'tag',
//       formatter: (param: any) => {
//         const cellValue = param.row.status;
//         return [StatusMap[cellValue] ?? cellValue ?? ''] as any;
//       },
//     },
//     {
//       field: 'priority',
//       title: '优先级',
//       customType: 'tag',
//       formatter: (param: any) => {
//         const cellValue = param.row.priority;
//         return [PriorityMap[cellValue] ?? cellValue ?? ''] as any;
//       },
//     },
//     {
//       field: 'responseLevel',
//       title: '响应级别',
//       customType: 'tag',
//       formatter: (param: any) => {
//         const cellValue = param.row.responseLevel;
//         return [ResponseLevelMap[cellValue] ?? cellValue ?? ''] as any;
//       },
//     },
//     {
//       field: 'action',
//       title: '操作',
//       width: 220,
//     },
//   ];
// }

// 编辑、新建表单选项
export function getFormOption(): CustomFormSchema[] {
  return [
    {
      fieldName: 'cmIncidentType',
      label: '事件类型',
      rules: 'required',
    },
    {
      fieldName: 'location',
      label: '发生地点',
      rules: 'required',
    },
    {
      fieldName: 'occurTime',
      label: '发生时间',
      rules: 'required',
      component: 'DatePicker',
      componentProps: {
        showTime: true,
      },
    },
    {
      fieldName: 'description',
      label: '事件描述',
      rules: 'required',
      component: 'Textarea',
    },
    // 状态
    {
      fieldName: 'status',
      label: '状态',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(StatusMap),
      },
    },
    {
      fieldName: 'severity',
      label: '严重程度',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(SeverityMap),
      },
    },
    {
      fieldName: 'priority',
      label: '优先级',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(PriorityMap),
      },
    },
    {
      fieldName: 'responseLevel',
      label: '响应级别',
      rules: 'required',
      component: 'Select',
      componentProps: {
        options: Object.values(ResponseLevelMap),
      },
    },
  ];
}
