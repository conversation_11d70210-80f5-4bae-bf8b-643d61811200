<!--
 * @Author: Strayer
 * @Date: 2025-08-18
 * @LastEditors: Strayer
 * @LastEditTime: 2025-08-18
 * @Description:
 * @FilePath: \energy-web\apps\web-antd\src\views\energy-management\carbon-emission-alarm-record\index.vue
-->
<script lang="ts" setup>
import type { ShallowRef } from 'vue';
import type { ComponentExposed } from 'vue-component-type-helpers';

import type { VxeGridPropTypes } from '#/adapter/vxe-table';
import type { WarningReportDTO } from '#/api/core/energy-management/alarm-record';
import type { CustomColumnSchema } from '#/components/grid-form';

import { ref, shallowRef, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { getAlarmRecordListApi } from '#/api/core/energy-management/alarm-record';
import GridForm from '#/components/grid-form/index.vue';

import { getColumnsScheme } from './data';

const gridFormRef =
  useTemplateRef<ComponentExposed<typeof GridForm>>('gridFormRef');

const hadLoad = ref(true);
const columnsScheme: ShallowRef<CustomColumnSchema<WarningReportDTO>> =
  shallowRef(getColumnsScheme());

/**
 * @description: 获取表格数据
 */
async function query(
  param: VxeGridPropTypes.ProxyAjaxQueryParams<WarningReportDTO>,
  fromData: any,
) {
  return getAlarmRecordListApi(param.page, { ...fromData, type: 4 });
}
</script>

<template>
  <Page auto-content-height>
    <template v-if="hadLoad">
      <GridForm
        ref="gridFormRef"
        title="异常提醒"
        :columns-scheme="columnsScheme"
        :query="query"
      />
    </template>
  </Page>
</template>
